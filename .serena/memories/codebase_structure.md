# Codebase Structure

## Directory Layout
```
uown-core/
├── app/
│   ├── models/          # ActiveRecord models with concerns
│   ├── services/        # Service objects (Mango, Nuapay, Shufti, FlgCrm)
│   ├── jobs/           # Background jobs (Sidekiq)
│   ├── mailers/        # Email mailers
│   ├── controllers/    # Engine controllers
│   ├── views/          # HAML templates (especially email/)
│   ├── validators/     # Custom validators
│   └── notifiers/      # Notification services
├── db/
│   └── migrate/        # Database migrations
├── spec/
│   ├── models/         # Model tests
│   ├── services/       # Service tests
│   ├── factories/      # FactoryBot factories
│   └── support/        # Test support files
├── lib/                # Library code and engine definition
└── config/             # Engine configuration
```

## Key Model Concerns
- `UserStates`: AASM state machine for user lifecycle
- `UserBank`: Bank account and wallet management
- `UserCertification`: Investment certification levels
- `UserWallet`: Mangopay wallet integration
- `SharedOrder`: Common share order functionality
- `PropertyDividendCalculations`: Dividend calculation logic

## Service Architecture
- `Mango::*`: Mangopay API integration services
- `Nuapay::*`: Nuapay API integration services
- `Shufti::*`: Shufti Pro KYC verification services
- `FlgCrm::*`: CRM integration services

## Background Jobs
- `DividendJob`: Process property dividend distributions
- `ShareOrderJob`: Handle share order processing
- `SyncKycDocumentsJob`: Sync KYC verification status
- `UserWalletReportJob`: Generate wallet reports