# Coding Conventions and Style Guidelines

## Ruby/Rails Conventions
- **Ruby Version**: 3.3.6
- **Rails Version**: ~> 7.0.1
- **File Structure**: Follow Rails Engine conventions
- **Naming**: Use snake_case for files, methods, and variables
- **Classes**: Use PascalCase for class names
- **Constants**: Use SCREAMING_SNAKE_CASE

## Model Conventions
- Use concerns for shared functionality (app/models/concerns/)
- Currency amounts stored in pence (divide by 100 for display)
- State machines use AASM gem
- Model validations and associations clearly defined

## Service Object Pattern
- Services inherit from BaseService classes
- Use Result objects for service responses
- Services grouped by integration (Mango, Nuapay, Shufti, FlgCrm)
- Each service has single responsibility

## Testing Conventions
- Use RSpec for testing framework
- FactoryBot for test data creation
- shoulda-matchers for validation testing
- Test files mirror source file structure
- Use descriptive test names and contexts

## Email Templates
- Use HAML templates in app/views/email/
- Support both HTML and text formats
- Custom email components for consistent styling

## Background Jobs
- Use Sidekiq for background processing
- Jobs inherit from ApplicationJob
- Error handling and retry logic included

## Database
- Migrations auto-included in parent applications
- Use UUIDv7 for primary keys where appropriate
- Follow Rails migration conventions