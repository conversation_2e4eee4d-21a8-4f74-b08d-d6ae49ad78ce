# UOWN Core Project Overview

## Purpose
UOWN Core is a Rails Engine (Ruby gem) that provides shared functionality for property investment applications. It serves as the core foundation for:
- uown-admin (administration interface)
- uown-api (API service)  
- uown-transaction-processor (transaction processing service)

## Description
The engine provides models, migrations, mailers, jobs, and services for managing:
- Property investments and share trading
- User management with KYC (Know Your Customer) verification
- Payment processing via Mangopay and Nuapay
- Share trading (buy/sell/transfer/easy-exit orders)
- Dividend distribution
- Document management

## Tech Stack
- **Ruby**: 3.3.6
- **Rails**: ~> 7.0.1
- **Database**: PostgreSQL (production), SQLite3 (development/test)
- **Payment Processors**: Mangopay (primary), Nuapay (bank transfers)
- **KYC Verification**: Shufti Pro
- **Background Jobs**: Sidekiq
- **Testing**: RSpec with FactoryBot
- **Templates**: HAML for email templates
- **State Management**: AASM for state machines
- **Container**: Docker support with docker-compose