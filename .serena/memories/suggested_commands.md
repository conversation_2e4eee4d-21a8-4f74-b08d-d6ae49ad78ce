# Suggested Development Commands

## Testing Commands
```bash
# Run all tests (direct)
bundle exec rspec

# Run specific test file
bundle exec rspec spec/path/to/spec_file_spec.rb

# Default rake task (runs tests)
rake

# Docker testing commands
docker-compose run --rm tests
docker-compose run --rm tests-only-failures
```

## Docker Development Commands
```bash
# Build Docker image (required after gem changes)
DOCKER_BUILDKIT=1 docker build -t `source .env && echo $DOCKER_IMAGE` --ssh default .

# Run test console
docker-compose run --rm test-console

# Update gems in Docker
docker-compose run --rm console
./bin/bundle install
```

## Gem Management
```bash
# Install gems (direct)
bundle install

# Install gems (Docker)
docker-compose run --rm console
./bin/bundle install
```

## Common Git Commands (Darwin)
```bash
git status
git add .
git commit -m "message"
git push origin feature-branch
git checkout development
git pull origin development
```

## System Commands (Darwin)
```bash
ls -la        # List files with details
find . -name "*.rb" | head -10    # Find Ruby files
grep -r "pattern" app/            # Search in app directory
cd path/to/directory              # Change directory
```