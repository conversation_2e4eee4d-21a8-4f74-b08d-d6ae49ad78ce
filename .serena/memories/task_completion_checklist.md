# Task Completion Checklist

## When a coding task is completed, ensure:

### Testing
1. **Run the test suite**: `bundle exec rspec` or `docker-compose run --rm tests`
2. **Verify specific tests**: Run tests for modified files
3. **Check test coverage**: Ensure new code is properly tested
4. **Factory updates**: Update FactoryBot factories if models changed

### Code Quality
1. **No linting available**: This project doesn't have RuboCop configured
2. **Follow Rails conventions**: Ensure code follows established patterns
3. **Service objects**: Use appropriate service patterns for business logic
4. **State machines**: Properly implement AASM states if needed

### Integration Points
1. **Engine compatibility**: Ensure changes work with parent applications
2. **Migration safety**: Database migrations are backward compatible
3. **API integrations**: Test external service integrations (Mango, Nuapay, Shufti)
4. **Background jobs**: Verify job processing works correctly

### Documentation
1. **Update specs**: Ensure RSpec tests cover new functionality
2. **Migration comments**: Add clear comments to complex migrations
3. **Service documentation**: Document complex service object logic

### Before Deployment
1. **Test in Docker**: `docker-compose run --rm tests`
2. **Database migrations**: Ensure migrations can be run safely
3. **Dependencies**: Check if Gemfile changes require Docker rebuild
4. **Integration testing**: Test with parent applications if possible

### Important Notes
- **Currency handling**: All amounts in pence, convert for display
- **KYC requirements**: Verify investment activities require proper verification
- **State transitions**: Test user state machine transitions thoroughly