# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

UOWN Core is a Rails Engine that provides shared functionality for property investment applications including uown-admin, uown-api, and uown-transaction-processor. This is a Ruby gem that contains models, migrations, mailers, jobs, and services for managing property investments, user management with KYC verification, share trading, and payment processing.

## Development Commands

### Testing
```bash
# Run all tests
bundle exec rspec

# Run tests via Docker
docker-compose run --rm tests

# Run only failed tests
docker-compose run --rm tests-only-failures

# Run specific test file
bundle exec rspec spec/path/to/spec_file_spec.rb

# Default rake task runs tests
rake
```

### Docker Development
```bash
# Build Docker image (required after gem changes)
DOCKER_BUILDKIT=1 docker build -t `source .env && echo $DOCKER_IMAGE` --ssh default .

# Run test console
docker-compose run --rm test-console

# Update gems in Docker
docker-compose run --rm console
./bin/bundle install
```


## Architecture Overview

### Core Models
- **User**: Central user model with concerns for authentication, KYC verification, wallet management, certification, and state management
- **Property**: Investment properties with share trading, dividend distribution, and document management
- **Share System**: Order management (buy/sell/transfer/easy-exit), share logs, and transaction tracking
- **Payment System**: Integration with Mangopay and Nuapay for payments, mandates, and payouts

### Key Concerns (app/models/concerns/)
- `UserStates`: AASM state machine for user lifecycle
- `UserBank`: Bank account and wallet management
- `UserCertification`: Investment certification levels
- `UserWallet`: Mangopay wallet integration
- `SharedOrder`: Common share order functionality
- `PropertyDividendCalculations`: Dividend calculation logic

### Payment Integration
- **Mangopay**: Primary payment processor (cards, bank wires, direct debits)
- **Nuapay**: Bank transfer integration
- **Shufti Pro**: KYC document verification

### Background Jobs (app/jobs/)
- `DividendJob`: Process property dividend distributions
- `ShareOrderJob`: Handle share order processing
- `SyncKycDocumentsJob`: Sync KYC verification status
- `UserWalletReportJob`: Generate wallet reports

### Services (app/services/)
- `Mango::*`: Mangopay API integration services
- `Nuapay::*`: Nuapay API integration services
- `Shufti::*`: Shufti Pro KYC verification services
- `FlgCrm::*`: CRM integration services

### Email System
Uses HAML templates with custom email components (app/views/email/) for consistent styling across mailers. Supports both HTML and text formats.

## Important Notes

- This is a Rails Engine gem, not a standalone application
- Database migrations are automatically included in parent applications
- Factories are exposed to parent projects via engine initializer
- Uses Ruby 3.3.6 and Rails ~> 7.0.1
- All currency amounts stored in pence (divide by 100 for display)
- KYC verification required for investment activities
- State machines control user progression through verification and investment processes