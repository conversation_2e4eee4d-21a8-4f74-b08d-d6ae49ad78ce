# TAG Link https://hub.docker.com/r/phusion/passenger-ruby33/tags
FROM phusion/passenger-ruby33:3.1.0

# How many bundler jobs to run in parallel - set with --build-arg="BUNDLER_JOBS=2"
ARG BUNDLER_JOBS=1

# Note: This Dockerfile is roughly in reverse order of likelihood to change for performance (so cached layers
#       at the bottom of the stack can be re-used rather than rebuilt)

# Set correct environment variables.
ENV HOME /root

# Use baseimage-docker's init process.
CMD ["/sbin/my_init"]

# Install public keys for hosts
RUN mkdir -p -m 0600 /home/<USER>/.ssh \
  && ssh-keyscan bitbucket.org >> /home/<USER>/.ssh/known_hosts

# Add your apt-get packages as part of this step
RUN apt-get update -qq \
  && DEBIAN_FRONTEND=noninteractive apt-get install -yq --no-install-recommends \
  imagemagick shared-mime-info \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /home/<USER>/webapp/tmp \
  && truncate -s 0 /var/log/*log

# Switch to app user - which runs the application process - to bundle install
USER app:app

# Set HOME env var used by bundler to store bundle and config files
ENV HOME /home/<USER>

WORKDIR /home/<USER>/webapp/

# COPY the Rails app - See .dockerignore file for what is excluded from being copied into the image
COPY --chown=app:app . /home/<USER>/webapp/

# Mount the ssh-agent from the host for the app user (uid: 9999) and install the bundle
RUN --mount=type=ssh,uid=9999 bundle config path /home/<USER>/.bundle \
  && bundle install --jobs $BUNDLER_JOBS

# Switch back to root user, because base image requires it to correctly run
# See: https://github.com/phusion/passenger-docker/issues/250
USER root
