# UOWN Core

Hooked On Phonics Core is a [Rails Engine](https://guides.rubyonrails.org/engines.html) which provides the shared functionality between the following projects:

* https://bitbucket.org/dubitplatform/uown-admin/
* https://bitbucket.org/dubitplatform/uown-api/
* https://bitbucket.org/dubitplatform/uown-transaction-processor/

# How to use this Readme

This Readme contains two sets of instructions:
* Steps for building and testing the application through Docker (recommended for maximum isolation and simplicity), and
* Steps for testing the application directly on your host system.

Follow the instructions for each section under the **Using Docker** or **Directly on host system** sub-headings, depending on your preferred choice.

# Setting up the repository for development

## Downloading the source

Clone the repo (including submodules):

```bash
<NAME_EMAIL>:dubitplatform/uown-core.git uown-core && cd uown-core
```

Check out the `development` branch:

```bash
git checkout development
```

At this point, you should switch to the `development` branch version of this Readme for the most up-to-date instructions.

## Installing and running the correct runtime

### Using Docker

#### Building the Docker container

The Dockerfile requires the `--ssh default` Docker Buildkit option to allow connecting your host machine's SSH agent to the Docker Daemon for the build process (in order to authenticate with Bitbucket for downloading the private repositories). Because Docker Compose [doesn't currently support this option](https://github.com/docker/compose/issues/7025) you need to build the Docker image using `docker build` separately:

**Note:** You will need to rebuild the image every time you upgrade Ruby or change the gems in the bundle. This means it's likely good practice to build the docker image whenever you switch branches or commits.

```bash
# If your host machine has 4 CPU cores or above, it will be faster to run bundler in parallel:
DOCKER_BUILDKIT=1 docker build -t `source .env && echo $DOCKER_IMAGE` --ssh default --build-arg BUNDLER_JOBS=3 .

# Otherwise, or if you're not sure
DOCKER_BUILDKIT=1 docker build -t `source .env && echo $DOCKER_IMAGE` --ssh default .
```

### Directly on host system

#### Installing the correct runtime version

If you're going to use your host system, it's recommended you [install rvm](https://rvm.io/rvm/install) before proceeding. The version of rvm that you use is not particularly important - you're encouraged to regularly update to get the latest bugfixes and features.

Use `rvm` to install the correct Ruby language version:

```bash
rvm install `cat .ruby-version`
```

#### Installing the correct versions of dependencies

Install the same version of Bundler for that Ruby version, as has previously been used for managing the Gemfile:

```bash
which bundler || gem install bundler -v `sed -n -e '/BUNDLED WITH/,1{n;p;}' Gemfile.lock
```

Use `bundler` to install the application's gems:

```
bundle install
```

## Running the test suite

### Using Docker

You can run the tests after you've built the Docker image:

```bash
docker-compose run --rm tests
```

### Directly on host system

The entire test suite can be run directly with RSpec:

```bash
bundle exec rspec
```

## Adding gems to the bundle or modifying the Gemfile

To add gems to the application's bundle, you need to list them in the `Gemfile`.

### Using Docker

Stop any running service:

```bash
docker-compose down
```

Run the console services and generate the new `Gemfile.lock`:

```bash
docker-compose run --rm console
./bin/bundle install
```

Then rebuild the Docker image, to include the new gems (see build command above). You can now run your services with the new gem available.

### Directly on host system

Install the bundle directly on your host system:

```bash
bundle install
```
