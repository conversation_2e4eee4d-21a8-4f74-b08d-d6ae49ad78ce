module EmailHelper
  def email_image_tag(image, **options)
    image_tag email_asset(image), **options
  end

  def email_paragraph(text, last: false)
    render 'email/paragraph', text: text, last: last
  end

  def list_item(legend, content = '')
    render 'email/list_item', legend: legend, content: content
  end

  def bullet_point(content)
    render 'email/bullet_point', content: content
  end

  def email_link_to(name = nil, options = nil, html_options = {}, &block)
    html_options[:style] = 'color: #646363; text-decoration: underline; font-weight: bold;'

    link_to(name, options, html_options, &block)
  end

  def email_mail_to(email, text = nil)
    text = text.present? ? text : email
    mail_to(text, email, style: 'color: #646363; text-decoration: underline; font-weight: bold;')
  end

  def email_button(text, link)
    render 'email/button', text: text, link: link
  end

  def email_asset(image)
    attachments.inline[image] = File.read(UownCore::Engine.root.join('app', 'assets', 'images', 'email', image))
    attachments[image].url
  end
end
