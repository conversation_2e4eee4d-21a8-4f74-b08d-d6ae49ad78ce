# Module: jobs

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
* `{Method1}`: {description}
* `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
* [Task1]: {brief description}
* [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
* [Task1]: {brief description}
* [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---

---KEY_DEFINITIONS_START---
Key Definitions:
2E#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/jobs
2E1#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/jobs/cancel_order_job.rb
2E2#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/jobs/dividend_job.rb
2E3#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/jobs/kyc_status_renew_job.rb
2E4#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/jobs/share_order_job.rb
2E5#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/jobs/sync_flg_to_crm_job.rb
2E6#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/jobs/sync_kyc_documents_job.rb
2E7#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/jobs/update_users_aasm_state_job.rb
2E8#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/jobs/user_wallet_report_job.rb
---KEY_DEFINITIONS_END---

last_KEY_edit: Assigned keys: 2E, 2E1, 2E1, 2E1, 2E2, 2E2, 2E3, 2E3, 2E4, 2E4, 2E5, 2E5, 2E6, 2E6, 2E7, 2E7, 2E8, 2E8
last_GRID_edit: Manual dep: user_wallet_report_job.rb -> [2E1#1(cancel_order_job.rb), 2E2#1(dividend_job.rb), 2E3#1(kyc_status_renew_job.rb), 2E4#1(share_order_job.rb), 2E5#1(sync_flg_to_crm_job.rb), 2E6#1(sync_kyc_documents_job.rb), 2E7#1(update_users_aasm_state_job.rb)] (n) (2025-06-10T10:38:02.085761)

---GRID_START---
X 2E#1 2E1#1 2E2#1 2E3#1 2E4#1 2E5#1 2E6#1 2E7#1 2E8#1
2E#1 = ox8
2E1#1 = xon7
2E2#1 = xnon6
2E3#1 = xnnon5
2E4#1 = xn3on4
2E5#1 = xn4on3
2E6#1 = xn5onn
2E7#1 = xn6on
2E8#1 = xn7o
---GRID_END---

---mini_tracker_end---
