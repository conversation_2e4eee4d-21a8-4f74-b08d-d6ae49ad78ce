require 'reports/monthly'

class AdminNotificationMailer < ApplicationMailer
  default from: Rails.application.secrets.email[:from_address],
          reply_to: Rails.application.secrets.email[:from_address],
          to: Rails.application.secrets.email[:admin_address]

  def cause_contribution(buy_order)
    @buy_order = buy_order
    mail
  end

  def easy_exit_order(easy_exit_order)
    @easy_exit_order = easy_exit_order
    mail
  end

  def locked(user)
    @user = user
    mail
  end

  def payment_failure(payment_log)
    @payment_log = payment_log
    mail
  end

  def shares_purchased(buy_order)
    @buy_order = buy_order
    mail
  end

  def signup(user)
    @user = user
    mail
  end

  def user_wallet_report(file)
    @filename = file.basename
    attachments[filename] = file
    mail
  end

  def monthly_report(user)
    report = Reports::Monthly.new(start_date: Time.now.last_month.beginning_of_month,
                                  end_date: Time.now.last_month.end_of_month)

    @kpis = report.kpis

    mail(to: user.email)
  end
end
