class CertificationMailer < ApplicationMailer
  [:first, :second, :third].each do |method_name|
    define_method(method_name) do |user|
      @user = user

      mail(to: user.email)
    end
  end

  [:first_timeout, :second_timeout].each do |method_name|
    define_method(method_name) do |user|
      @user = user
      @link = "#{Rails.application.secrets.frontend_url}/users/certifications/new"

      mail(to: user.email)
    end
  end
end
