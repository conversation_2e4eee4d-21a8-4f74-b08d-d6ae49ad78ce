class KYCMailer < ApplicationMailer
  def regular_failure(user, msg)
    @user = user
    @msg = msg
    mail(to: '<EMAIL>')
  end

  def successful(user)
    @user = user

    mail(to: user.email)
  end

  def failure(user, failure_reason)
    @user = user
    @failure_reason = failure_reason
    @link = "#{Rails.application.secrets.frontend_url}/users/kycs/regular"

    mail(to: user.email)
  end

  def document_outdated(user)
    @user = user
    @link = "#{Rails.application.secrets.frontend_url}/users/account/verify"

    mail(to: user.email)
  end
end
