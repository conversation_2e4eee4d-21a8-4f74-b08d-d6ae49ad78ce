# Module: mailers

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
* `{Method1}`: {description}
* `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
* [Task1]: {brief description}
* [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
* [Task1]: {brief description}
* [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---

---KEY_DEFINITIONS_START---
Key Definitions:
2F#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers
2F1#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/.keep
2F2#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/admin_notification_mailer.rb
2F3#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/application_mailer.rb
2F4#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/bank_mailer.rb
2F5#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/certification_mailer.rb
2F6#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/contact_mailer.rb
2F7#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/kYC_mailer.rb
2F8#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/payins_bank_wire_mailer.rb
2F9#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/payins_card_mailer.rb
2F10: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/payins_direct_debit_mailer.rb
2F11: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/payouts_bank_wire_mailer.rb
2F12: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/property_dividend_mailer.rb
2F13: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/sca_mailer.rb
2F14: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/share_buy_order_mailer.rb
2F15: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/share_easy_exit_order_mailer.rb
2F16: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/share_sell_order_mailer.rb
2F17: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/share_transfer_order_mailer.rb
2F18: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/user_address_mailer.rb
2F19: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/user_locked_mailer.rb
2F20: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/user_login_mailer.rb
2F21: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/user_password_mailer.rb
2F22: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/mailers/user_pending_mailer.rb
---KEY_DEFINITIONS_END---

last_KEY_edit: Assigned keys: 2F, 2F1, 2F1, 2F1, 2F2, 2F2, 2F2, 2F3, 2F3, 2F3, 2F4, 2F4, 2F5, 2F5, 2F6, 2F6, 2F7, 2F7, 2F8, 2F8, 2F9, 2F9, 2F10, 2F11, 2F12, 2F13, 2F14, 2F15, 2F16, 2F17, 2F18, 2F19, 2F20, 2F21, 2F22
last_GRID_edit: Manual dep: user_pending_mailer.rb -> [2F1#1(.keep), 2F10(payins_direct_debit_mailer.rb), 2F11(payouts_bank_wire_mailer.rb), 2F12(property_dividend_mailer.rb), 2F13(sca_mailer.rb), 2F14(share_buy_order_mailer.rb), 2F15(share_easy_exit_order_mailer.rb), 2F16(share_sell_order_mailer.rb), 2F17(share_transfer_order_mailer.rb), 2F18(user_address_mailer.rb), 2F19(user_locked_mailer.rb), 2F2#1(admin_notification_mailer.rb), 2F20(user_login_mailer.rb), 2F21(user_password_mailer.rb), 2F4#1(bank_mailer.rb), 2F5#1(certification_mailer.rb), 2F6#1(contact_mailer.rb), 2F7#1(kYC_mailer.rb), 2F8#1(payins_bank_wire_mailer.rb), 2F9#1(payins_card_mailer.rb)] (n) (2025-06-10T11:39:36.552879)

---GRID_START---
X 2F#1 2F1#1 2F2#1 2F3#1 2F4#1 2F5#1 2F6#1 2F7#1 2F8#1 2F9#1 2F10 2F11 2F12 2F13 2F14 2F15 2F16 2F17 2F18 2F19 2F20 2F21 2F22
2F#1 = ox22
2F1#1 = xon21
2F2#1 = xno<n19
2F3#1 = xnnon19
2F4#1 = xnn<on18
2F5#1 = xnn<non17
2F6#1 = xnn<nnon16
2F7#1 = xnn<n3on15
2F8#1 = xnn<n4on14
2F9#1 = xnn<n5on13
2F10 = xnn<n6on12
2F11 = xnn<n7on11
2F12 = xnn<n8on10
2F13 = xnn<n9on9
2F14 = xnn<n10on8
2F15 = xnn<n11on7
2F16 = xnn<n12on6
2F17 = xnn<n13on5
2F18 = xnn<n14on4
2F19 = xnn<n15on3
2F20 = xnn<n16onn
2F21 = xnn<n17on
2F22 = xnn<n18o
---GRID_END---

---mini_tracker_end---
