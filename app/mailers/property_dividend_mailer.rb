class PropertyDividendMailer < ApplicationMailer
  include ActionView::Helpers::NumberHelper
  include CurrencyHelper

  # Rent / Interest Payments

  def rent(property_dividend)
    @property_dividend = property_dividend
    @user = property_dividend.user
    @property = property_dividend.property
    @amount = property_dividend.amount_to_pay

    @link = investment_page_link

    mail(to: @user.email, subject: default_i18n_subject(amount: currency(@amount, unit: '£'),
                                                        property: @property.name))
  end

  def interest(property_dividend)
    @property_dividend = property_dividend
    @user = property_dividend.user
    @property = property_dividend.property
    @amount = property_dividend.amount_to_pay

    @link = investment_page_link

    mail(to: @user.email, subject: default_i18n_subject(amount: currency(@amount, unit: '£'),
                                                        property: @property.name))
  end

  # Property Payouts

  def development(property_dividend)
    @property_dividend = property_dividend
    @user = property_dividend.user
    @property = property_dividend.property
    @amount = property_dividend.amount_to_pay

    exit_order = property_dividend.payout
                                  .exit_orders
                                  .where(user: @user)
                                  .first

    @total_invested = exit_order ? exit_order.total_amount : 0

    @link = investment_page_link

    mail(to: @user.email)
  end

  def loan(property_dividend)
    @property_dividend = property_dividend
    @user = property_dividend.user
    @property = property_dividend.property

    # Include any interest payments in the total amount
    @amount = property_dividend.amount_to_pay + total_dividends_for_property(property_dividend)

    exit_order = property_dividend.payout
                                  .exit_orders
                                  .where(user: @user)
                                  .first

    @total_invested = exit_order ? exit_order.total_amount : 0

    @link = investment_page_link

    mail(to: @user.email)
  end

  def property(property_dividend)
    @property_dividend = property_dividend
    @user = property_dividend.user
    @property = property_dividend.property
    @amount = property_dividend.amount_to_pay

    exit_order = property_dividend.payout
                                  .exit_orders
                                  .where(user: @user)
                                  .first

    # Returns
    @total_invested = @user.share_logs.where(property: @property)
                                      .where.not(sell_order_id: exit_order.id)
                                      .sum(:total_amount)

    @total_payout = (exit_order ? exit_order.total_amount : 0) + @amount
    @total_growth = @total_payout - @total_invested

    @growth_percentage = @total_growth.to_d / @total_invested * 100

    # Rent
    @total_rent = total_dividends_for_property(property_dividend)
    @total_rent_percentage = (@total_rent.to_d / @total_invested) * 100

    @link = investment_page_link

    mail(to: @user.email)
  end

  private

  def investment_page_link
    "#{Rails.application.secrets.frontend_url}/investments"
  end

  def total_dividends_for_property(property_dividend)
    user = property_dividend.user

    dividends = user.dividends
                    .where(property_id: property_dividend.property_id)
                    .where.not(id: property_dividend.id)

    user.payment_logs
        .where(dividend_id: dividends.ids)
        .sum(:amount) || 0
  end
end
