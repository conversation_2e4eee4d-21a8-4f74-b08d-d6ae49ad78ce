class ScaMailer < ApplicationMailer
  def enroll(user)
    @user = user
    @link = "#{Rails.application.secrets.frontend_url}/users/confirmations/sca_enroll"

    mail(to: user.email, subject: 'Action Required: Please Complete Your Strong Customer Authentication (SCA)')
  end

  def success(user)
    @user = user

    mail(to: user.email, subject: 'Your SCA is complete')
  end

  def failure(user)
    @user = user
    @link = "#{Rails.application.secrets.frontend_url}/users/confirmations/sca_enroll"

    mail(to: user.email, subject: 'Your SCA Failed – Action Required')
  end
end
