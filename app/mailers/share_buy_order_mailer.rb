class ShareBuyOrderMailer < ApplicationMailer
  include ActionView::Helpers::NumberHelper
  include <PERSON><PERSON><PERSON>cy<PERSON>el<PERSON>

  def bought(buy_order)
    @user = buy_order.user
    @buy_order = buy_order
    @link = "#{Rails.application.secrets.frontend_url}/investments/#{buy_order.id}/receipt.pdf"

    mail(to: @user.email, subject: default_i18n_subject(quantity: buy_order.quantity,
                                                        property: buy_order.property.name))
  end

  def loan(buy_order)
    @user = buy_order.user
    @buy_order = buy_order

    if buy_order.property.acknowledgement_pdf.attached?
      attachments['acknowledgement.pdf'] = buy_order.property.acknowledgement_pdf.download
    end

    mail(to: @user.email, subject: default_i18n_subject(amount: currency(buy_order.total_amount, unit: '£'),
                                                        property: buy_order.property.name))
  end

  def contributed(buy_order)
    @user = buy_order.user
    @buy_order = buy_order
    @link = "#{Rails.application.secrets.frontend_url}/investments/#{buy_order.id}/receipt.pdf"

    mail(to: @user.email, subject: default_i18n_subject(property: buy_order.property.name))
  end

  def failed(user, reason = '')
    @user = user
    @reason = reason.parameterize.underscore

    mail(to: @user.email)
  end
end
