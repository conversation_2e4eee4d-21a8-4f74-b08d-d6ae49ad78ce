class ShareEasyExitOrderMailer < ApplicationMailer
  def created(easy_exit_order)
    @user = easy_exit_order.user
    @easy_exit_order = easy_exit_order

    mail(to: @user.email)
  end

  def completed(easy_exit_order)
    @user = easy_exit_order.user
    @easy_exit_order = easy_exit_order
    @quantity = easy_exit_order.quantity

    mail(to: @user.email, subject: default_i18n_subject(quantity: @quantity,
                                                        property: @easy_exit_order.property.name))
  end

  def rejected(easy_exit_order)
    @user = easy_exit_order.user
    @easy_exit_order = easy_exit_order

    mail(to: @user.email)
  end
end
