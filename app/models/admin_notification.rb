class AdminNotification
  def self.signup(user)
    admin_notification_delivery('signup', user)
  end

  def self.locked(user)
    admin_notification_delivery('locked', user)
  end

  def self.cause_contribution(buy_order)
    admin_notification_delivery('cause_contribution', buy_order)
  end

  def self.shares_purchased(buy_order)
    admin_notification_delivery('shares_purchased', buy_order)
  end

  def self.payment_failure(payment_log)
    admin_notification_delivery('payment_failure', payment_log)
  end

  def self.easy_exit_order(easy_exit_order)
    admin_notification_delivery('easy_exit_order', easy_exit_order)
  end

  def self.mailer
    AdminNotificationMailer
  end

  def self.enabled?
    Rails.application.secrets.email[:admin_address].present? rescue false
  end

  def self.admin_notification_delivery(action, object)
    return unless enabled?

    AdminNotificationDelivery.notify(action.to_sym, object)
  end
end
