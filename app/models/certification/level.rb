class Certification::Level < ActiveRecord::Base
  KINDS = %w(natural legal).freeze

  has_many :questions, dependent: :destroy
  has_many :users, foreign_key: :certification_level_id, dependent: :nullify

  has_many :property_certification_levels, foreign_key: :certification_level_id, dependent: :destroy
  has_many :properties, through: :property_certification_levels

  accepts_nested_attributes_for :questions, allow_destroy: true,
                                            reject_if: :all_blank

  has_one_attached :attachment

  validates :attachment, presence: true, content_type: %w[image/png image/jpeg image/gif]
  validates :name, presence: true
  validates :kind, presence: true, inclusion: { in: KINDS }

  before_save :set_default

  private

  def set_default
    Certification::Level.where(kind: kind).update_all(default: false) if default?
  end
end
