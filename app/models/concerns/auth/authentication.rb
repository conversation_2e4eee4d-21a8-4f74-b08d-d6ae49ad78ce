module Auth::Authentication
  extend ActiveSupport::Concern

  included do
    SESSION_EXPIRY = (Rails.application.secrets.session_timeout.presence || 30).minutes

    validates_strength_of :password, with: :email, level: :strong, if: :validate_password?
  end

  class_methods do
    def authenticate_from_token(id, authentication_token)
      user = User.find_by(id: id, authentication_token: authentication_token)

      if user && user.last_active_at >= SESSION_EXPIRY.ago
        user.update_column(:last_active_at, Time.now)

        return user
      end

      nil
    end
  end

  def ensure_authentication_token!
    self.authentication_token = generate_token(:authentication_token)
    self.last_active_at = Time.now
    save(validate: false)
  end

  def ensure_confirmation_token!
    self.confirmation_token = generate_token(:confirmation_token)
    save(validate: false)
  end

  def ensure_reset_token!
    self.reset_token = generate_token(:reset_token)
    save(validate: false)
  end

  private

  def validate_password?
    password.present?
  end

  def generate_token(column)
    loop do
      token = SecureRandom.urlsafe_base64(15).tr('lIO0', 'sxyz')
      break token unless User.find_by(column => token)
    end
  end
end
