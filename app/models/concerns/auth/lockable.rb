module Auth::Lockable
  extend ActiveSupport::Concern

  included do
    MAXIMUM_ATTEMPTS = 5
    UNLOCK_IN = 30.minutes
  end

  def login_success!(ip_address = nil, user_agent = nil)
    self.last_sign_in_at = current_sign_in_at
    self.current_sign_in_at = Time.zone.now
    self.last_sign_in_ip = current_sign_in_ip
    self.current_sign_in_ip = ip_address
    self.sign_in_count += 1
    save(validate: false)

    login_attempts.create!(state: 'success', ip: ip_address, user_agent: user_agent)

    unlock!
  end

  def login_failure!(ip_address = nil, user_agent = nil)
    login_attempts.create!(state: 'fail', ip: ip_address, user_agent: user_agent)

    increment_failed_attempts!
    lock! if attempts_exceeded?
  end

  def lock!
    self.locked_at = Time.now.utc
    save(validate: false)

    send_locked_notification
  end

  def unlock!
    self.locked_at = nil
    self.failed_attempts = 0
    save(validate: false)
  end

  def locked?
    locked_at.present? && !lock_expired?
  end

  private

  def increment_failed_attempts!
    increment!(:failed_attempts)
  end

  def attempts_exceeded?
    failed_attempts >= MAXIMUM_ATTEMPTS
  end

  def lock_expired?
    locked_at && locked_at < UNLOCK_IN.ago
  end

  def unlock_time
    locked_at + UNLOCK_IN
  end

  def send_locked_notification
    UserLockedDelivery.notify(:locked, self, unlock_time)
    AdminNotificationDelivery.notify(:locked, self)
  end
end
