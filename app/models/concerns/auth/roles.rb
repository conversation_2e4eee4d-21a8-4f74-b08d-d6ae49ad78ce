module Auth::Roles
  extend ActiveSupport::Concern

  included do
    has_many :user_roles, dependent: :destroy
    has_many :roles, through: :user_roles
  end

  def role?(role)
    role_names.include?(role)
  end

  def any_role?(roles)
    role_names.any? { |r| roles.include?(r) }
  end

  def add_role(role)
    UserRole.create(user: self, role: Role.find_or_create_by(name: role))
  end

  def remove_role(role)
    UserRole.find_by(user: self, role: Role.find_by(name: role)).destroy
  end

  def role_names
    user_roles.collect { |ur| ur.role.name.to_sym }
  end
end
