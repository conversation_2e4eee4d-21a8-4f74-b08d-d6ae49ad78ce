module PropertyDividendCalculations
  extend ActiveSupport::Concern

  def allocation_amount
    total_fees + dividends.sum(&:amount_to_pay) + exit_orders.sum(&:total_amount)
  end

  def total_fees
    fees.collect(&:amount).compact.sum
  end

  def subtotal
    amount - total_fees
  end

  def total_per_share
    subtotal.to_d / total_shares
  end

  def total_shares
    ::Share::Log.where(property_id: property_id).sum(:quantity)
  end
end
