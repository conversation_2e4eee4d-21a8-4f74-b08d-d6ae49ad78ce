module PropertyLabels
  extend ActiveSupport::Concern

  def finance_label
    self[:finance_label].presence || 'Finance'
  end

  def property_amount_label
    self[:property_amount_label].presence || 'Construction Costs'
  end

  def property_fee_legal_and_professional_label
    self[:property_fee_legal_and_professional_label].presence || 'Legal & Professional'
  end

  def site_value_label
    self[:site_value_label].presence || 'Site Value'
  end
end
