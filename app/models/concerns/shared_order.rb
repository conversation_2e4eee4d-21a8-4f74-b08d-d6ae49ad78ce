module SharedOrder
  extend ActiveSupport::Concern

  included do
    validate :check_sell_order, on: :create
  end

  def check_sell_order
    return unless property && user

    # Skip the following checks if the user is the root user
    return true if user == User.admin_uown

    # Only the root user can sell shares in an unfunded property
    errors.add(:base, "You can't sell shares in this property") unless property.funded? || property.easy_exit?

    # Check you have enough shares to sell
    errors.add(:base, "You don't have enough shares to sell that many") if quantity > quantity_available_to_sell
  end

  def mark_cancelled
    # This method is overriden in the transaction processor
    false
  end

  private
end
