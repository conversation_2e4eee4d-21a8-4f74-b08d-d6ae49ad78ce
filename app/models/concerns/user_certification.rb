module UserCertification
  extend ActiveSupport::Concern

  included do
    CERTIFICATION_FAILURE_TIMEOUT = 48.hours
  end

  def can_self_certify?
    return true if self_certified? ||
                   kyc_light_is_complete? ||
                   kyc_regular_is_required? ||
                   kyc_regular_is_complete? ||
                   recertification_required?

    if first_self_certification_failed? || second_self_certification_failed?
      return user_states.last.created_at < CERTIFICATION_FAILURE_TIMEOUT.ago
    end

    false
  end

  def last_failed_certification_at
    return user_states.last.created_at if first_self_certification_failed? ||
                                          second_self_certification_failed? ||
                                          certification_failed?

    nil
  end

  private

  def send_certification_failure_email
    CertificationMailer.first(self).deliver_later if first_self_certification_failed?
    CertificationMailer.second(self).deliver_later if second_self_certification_failed?
    CertificationMailer.third(self).deliver_later if certification_failed?
  end
end
