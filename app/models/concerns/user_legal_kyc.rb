module UserLegalKyc
  extend ActiveSupport::Concern

  included do
    MAX_FILESIZE = 10.megabytes.freeze
    LEGAL_TYPES = %w(BUSINESS SOLETRADER).freeze

    # KYC Light
    validates :business_name, :business_number, :country_of_residence, :date_of_birth,
              :nationality, :address, :title, :legal_type, presence: true, if: :kyc_light_is_complete?

    # KYC Normal
    validates :headquarters, presence: true, if: :kyc_regular_is_pending?

    # Id Document
    validates :identity_proof,
              :registration_proof,
              :articles_of_association,
              :shareholder_declaration, length: { maximum: MAX_FILESIZE,
                                                  message: 'the maximum filesize is 10mb' }

    validates :identity_proof,
              :registration_proof, presence: true, if: :documents_required?

    validates :articles_of_association, presence: true, if: :articles_of_association_required?
    validates :shareholder_declaration, presence: true, if: :shareholder_declaration_required?
  end

  def valid_kyc_documents?
    mangopay_kyc_documents.validated.exists?(kind: 'identity_proof') &&
    mangopay_kyc_documents.validated.exists?(kind: 'registration_proof') &&
    (legal_type == 'SOLETRADER' || mangopay_kyc_documents.validated.exists?(kind: 'articles_of_association')) &&
    (legal_type == 'SOLETRADER' || mangopay_kyc_documents.validated.exists?(kind: 'shareholder_declaration'))
  end

  private

  def documents_required?
    kyc_regular_is_pending? && mangopay_kyc_documents.pending.none?
  end

  def articles_of_association_required?
    documents_required? && legal_type == 'BUSINESS'
  end

  def shareholder_declaration_required?
    documents_required? && legal_type == 'BUSINESS'
  end
end
