module UserNaturalKyc
  extend ActiveSupport::Concern

  included do
    EMPLOYMENT_STATUS = ['Employed',
                         'Self-employed',
                         'Retired',
                         'Student',
                         'Not working'].freeze

    EXPERIENCE = ['Low',
                  'Medium',
                  'High'].freeze

    INCOME_RANGES = [['Under €18K', 1],
                     ['Between €18 and €30K', 2],
                     ['Between €30 and €50K', 3],
                     ['Between €50 and €80K', 4],
                     ['Between €80 and €120K', 5],
                     ['Over €120K', 6]].freeze

    MAX_FILESIZE = 10.megabytes.freeze

    PLANNED_INVESTMENT = ['£0 - £500',
                          '£500 - £5000',
                          '£5000 - £20000',
                          '£20000+',
                          '£0 - £5,000',
                          '£5,001 - £10,000',
                          '£10,001 - £25,000',
                          '£25,001 - £100,000',
                          '£100,000+'].freeze

    # KYC Light
    validates :country_of_residence, :date_of_birth,
              :nationality, :address, :title, presence: true, if: :kyc_light_is_complete?

    # KYC Normal
    validates :income_range, :occupation, presence: true,
                                          if: :kyc_regular_is_pending?

    # Id Document
    validates :identity_proof, presence: true,
                               length: { maximum: MAX_FILESIZE, message: 'the maximum filesize is 10mb' },
                               if: :identity_proof_required?

  end

  def valid_kyc_documents?
    mangopay_kyc_documents.validated.exists?(kind: 'identity_proof')
  end

  private

  def identity_proof_required?
    kyc_regular_is_pending? && mangopay_kyc_documents.pending.none?
  end
end
