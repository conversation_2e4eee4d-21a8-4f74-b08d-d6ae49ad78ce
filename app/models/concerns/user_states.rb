module UserStates
  extend ActiveSupport::Concern

  included do
    CERTIFICATION_FAILURE_TIMEOUT = 48.hours

    has_many :user_states, class_name: 'User::State', dependent: :destroy

    include AASM

    aasm do
      state :registered, initial: true
      state :kyc_light_is_complete
      # state :self_certified
      # state :first_self_certification_failed
      # state :second_self_certification_failed
      # state :certification_failed
      state :kyc_regular_is_required
      state :kyc_regular_is_pending
      state :kyc_regular_is_complete
      state :kyc_regular_has_failed
      state :rejected_country
      state :rejected_nationality
      state :rejected_age
      state :recertification_required

      after_all_transitions :log_status_change

      event :kyc_light_complete do
        transitions from: :registered,
                    to: :kyc_light_is_complete
      end

      event :self_certify do
        transitions from: [:kyc_light_is_complete,
                           :recertification_required,
                           :first_self_certification_failed,
                           :second_self_certification_failed],
                    to: :self_certified,
                    guard: :can_self_certify?
      end

      event :first_certification_failure, after_commit: :send_certification_failure_email do
        transitions from: [:kyc_light_is_complete,
                           :recertification_required],
                    to: :first_self_certification_failed,
                    guard: :can_self_certify?
      end

      event :second_certification_failure, after_commit: :send_certification_failure_email do
        transitions from: :first_self_certification_failed,
                    to: :second_self_certification_failed,
                    guard: :can_self_certify?
      end

      event :third_certification_failure, after_commit: :send_certification_failure_email do
        transitions from: :second_self_certification_failed,
                    to: :certification_failed,
                    guard: :can_self_certify?
      end

      event :kyc_regular_required do
        transitions from: :kyc_light_is_complete,
                    to: :kyc_regular_is_required
      end

      event :kyc_regular_submitted do
        transitions from: [:kyc_light_is_complete,
                           :kyc_regular_is_required,
                           :kyc_regular_has_failed,
                           ],
                    to: :kyc_regular_is_pending
      end

      event :kyc_regular_complete, after_commit: :process_queued_actions do
        transitions from: [:kyc_regular_is_pending,
                           ],
                    to: :kyc_regular_is_complete
      end

      event :kyc_regular_failed, after_commit: :cancel_queued_actions do
        transitions from: [:kyc_regular_is_pending,
                           ],
                    to: :kyc_regular_has_failed
      end

      event :reject_country do
        transitions to: :rejected_country
      end

      event :reject_nationality do
        transitions to: :rejected_nationality
      end

      event :reject_age do
        transitions to: :rejected_age
      end

      # event :recertify do
      #   transitions from: [:self_certified,
      #                      :first_self_certification_failed,
      #                      :second_self_certification_failed,
      #                      :certification_failed,
      #                      :kyc_regular_is_required,
      #                      :kyc_regular_is_pending,
      #                      :kyc_regular_is_complete,
      #                      :kyc_regular_has_failed],
      #               to: :recertification_required
      # end
    end

    # after_validation :create_or_update_remote_users
    after_save :run_sync_flg_to_crm
  end

  def create_or_update_remote_users
    # Don't continue if we have any other validation errors
    return if errors.any?

    # Only continue if they are changing to the 2 required states
    return unless aasm_state_changed? && (kyc_light_is_complete? || kyc_regular_is_pending?)

    # Mangopay
    is_a?(User::Legal) ? Mango::LegalUser.update(self) : Mango::NaturalUser.update(self)
  end

  def log_status_change
    user_states.create(before: aasm.from_state, after: aasm.to_state)
  end

  # Note: There was previously a bug where you had duplicate user_state records.  Using the last state only ended up
  # putting the user in an endless loop. So we have to check that the previous state isnt the same as their existing
  # state
  def previous_state
    user_states.where.not(before: aasm_state).last&.before
  end

  # def recertified!
  #   case previous_state
  #   when 'kyc_regular_is_required'
  #     kyc_regular_required!
  #   when 'kyc_regular_is_pending'
  #     kyc_regular_submitted!
  #   when 'kyc_regular_is_complete'
  #     kyc_regular_complete!
  #   when 'kyc_regular_has_failed'
  #     kyc_regular_failed!
  #   else
  #     self_certify!
  #   end
  # end

  # def certification_failed!
  #   case aasm_state
  #   when 'kyc_light_is_complete', 'recertification_required'
  #     first_certification_failure!
  #   when 'first_self_certification_failed'
  #     second_certification_failure!
  #   when 'second_self_certification_failed'
  #     third_certification_failure!
  #   else
  #     raise 'Invalid State'
  #   end
  # end

  def refresh_kyc_status!
    mangopay_kyc_documents.each(&:update_status)

    # Valid
    if valid_kyc_documents?
      kyc_regular_complete!

      KYCDelivery.notify(:successful, self)

      return true
    end

    # Failed
    if mangopay_kyc_documents.pending.reload.none?
      kyc_regular_failed!

      mangopay_kyc_documents.refused.each do |kyc_document|
        KYCDelivery.notify(:failure, self, kyc_document.failure_reason)
      end
    end

    false
  end

  def run_sync_flg_to_crm
    return if self.registered?
    return unless Rails.env.production?
    return unless saved_change_to_aasm_state? || saved_change_to_phone_number?

    SyncFlgToCrmJob.set(wait: 2.minutes).perform_later(self.reload.id)
  end
end
