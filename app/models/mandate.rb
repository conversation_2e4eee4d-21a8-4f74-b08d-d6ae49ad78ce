class Mandate < ActiveRecord::Base
  belongs_to :user

  attr_accessor :return_url

  validates :user, :bank_account_id, :amount, presence: true
  validates :day, numericality: { greater_than_or_equal_to: 1, less_than_or_equal_to: 28 }
  validates :amount, numericality: { greater_than: 0,
                                     less_than_or_equal_to: Mango::Mandate::LIM<PERSON> }
  validate :check_bank_account_belongs_to_user

  before_create :create_mangopay_mandate

  def self.due
    where('last_payment_date <= ? or last_payment_date is null', 1.month.ago.to_date).
    where(cancelled_at: nil, day: Date.today.day)
  end

  def bank_account
    Mango::BankAccount.find_by_id(user, bank_account_id)
  end

  def mangopay_mandate
    Mango::Mandate.find_by_id(mangopay_mandate_id) if mangopay_mandate_id.present?
  end

  def request_money
    raise 'User isnt KYC Regular' unless user.kyc_regular_is_complete?
    raise 'Payment already requested this month' unless payment_due?
    raise 'Mandate has been cancelled' if cancelled_at.present?

    Mango::Mandate.request_money(user: user,
                                 mandate_id: mangopay_mandate_id,
                                 amount: amount)

    update_attribute(:last_payment_date, Date.today)
  end

  def cancel!
    return false unless mangopay_mandate_id.present?

    Mango::Mandate.cancel(mangopay_mandate_id) unless mangopay_mandate.status == 'FAILED'

    update_attribute(:cancelled_at, Time.now)
  end

  private

  def check_bank_account_belongs_to_user
    return unless user && bank_account_id

    bank_account = Mango::BankAccount.find_by_id(user, bank_account_id) rescue nil

    errors.add(:bank_account_id, "doesn't exist or doesn't belong to you") unless bank_account
  end

  def payment_due?
    day == Date.today.day && (last_payment_date.nil? || last_payment_date <= 1.month.ago.to_date)
  end

  def create_mangopay_mandate
    mm = Mango::Mandate.create(bank_account_id: bank_account_id,
                               return_url: return_url)

    self.mangopay_mandate_id = mm.id
  end
end
