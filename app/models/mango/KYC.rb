class Mango::KYC
  attr_accessor :id, :type, :status, :refused_reason_type,
                :refused_reason_message, :owner_id

  def initialize(params = {})
    self.id = params['Id']
    self.type = params['Type']
    self.status = params['Status']
    self.refused_reason_type = params['RefusedReasonType']
    self.refused_reason_message = params['RefusedReasonMessage']
    self.owner_id = params['UserId']
  end

  def self.create(user:, kind:, image:)
    kyc_document = new(MangoPay::KycDocument.create(user.mangopay_id, Type: kind.upcase))

    create_page(user: user, kyc_document: kyc_document, image: image)

    kyc_document
  end

  def self.create_page(user:, kyc_document:, image:)
    MangoPay::KycDocument.create_page(user.mangopay_id, kyc_document.id, image)
    MangoPay::KycDocument.update(user.mangopay_id, kyc_document.id, Status: 'VALIDATION_ASKED')
  end

  def self.find_by_id(user:, kyc_document_id:)
    new(MangoPay::KycDocument.fetch(user.mangopay_id, kyc_document_id))
  end
end
