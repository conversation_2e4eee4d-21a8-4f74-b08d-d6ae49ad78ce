class Mango::BankAccount
  MAX_BANK_ACCOUNTS = 2

  attr_accessor :id, :address_1, :address_2, :city, :region, :post_code,
                :country, :name, :sort_code, :account_number, :active

  def initialize(params = {})
    self.id = params['Id']
    self.address_1 = params['OwnerAddress']['AddressLine1']
    self.address_2 = params['OwnerAddress']['AddressLine2']
    self.city = params['OwnerAddress']['City']
    self.region = params['OwnerAddress']['Region']
    self.post_code = params['OwnerAddress']['PostalCode']
    self.country = params['OwnerAddress']['Country']
    self.name = params['OwnerName']
    self.sort_code = params['SortCode']
    self.account_number = params['AccountNumber']
    self.active = params['Active']
  end

  def self.create(user:, address_1:, address_2:, city:, region:,
                  post_code:, country:, name:, sort_code:, account_number:)
    account = MangoPay::BankAccount.create(user.mangopay_id, OwnerAddress: {
                                                               AddressLine1: address_1,
                                                               AddressLine2: address_2,
                                                               City: city,
                                                               Region: region,
                                                               PostalCode: post_code,
                                                               Country: country
                                                             },
                                                             OwnerName: name,
                                                             SortCode: sort_code,
                                                             AccountNumber: account_number,
                                                             Type: 'gb')

    new(account)
  end

  def self.find_by_user(user)
    MangoPay::BankAccount.fetch_all(user.mangopay_id).collect { |b| new(b) }
  end

  def self.find_by_id(user, bank_account_id)
    new MangoPay::BankAccount.fetch(user.mangopay_id, bank_account_id)
  end

  def self.allow_new_account?(user)
    return true if user.mangopay_id.blank?

    bank_accounts = MangoPay::BankAccount.fetch(user.mangopay_id, Active: true)
    bank_accounts.count < MAX_BANK_ACCOUNTS
  end

  def self.deactivate(user, bank_account_id)
    new MangoPay::BankAccount.update(user.mangopay_id, bank_account_id, Active: false)
  end
end
