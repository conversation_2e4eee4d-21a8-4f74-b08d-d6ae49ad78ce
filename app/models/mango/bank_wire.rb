class Mango::BankWire
  UK_ACCOUNT_NUMBER = '********'.freeze
  UK_SORT_CODE = '20-00-00'.freeze

  attr_accessor :id, :amount, :bic, :fees, :iban, :owner_name, :reference, :type, :log_id

  def initialize(params = {}, log_id = nil)
    self.id = params['Id']
    self.amount = params['DeclaredDebitedFunds']['Amount'].to_i
    self.bic = params['BankAccount']['BIC']
    self.fees = params['DeclaredFees']['Amount'].to_i
    self.iban = params['BankAccount']['IBAN']
    self.owner_name = params['BankAccount']['OwnerName']
    self.reference = params['WireReference']
    self.type = params['BankAccount']['Type']
    self.log_id = log_id
  end

  def self.create(user:, amount:, potential_investment_id: nil)
    raise 'Amount cannot be blank' unless amount.present?

    wallet = user.wallet
    amount = amount.to_i

    bank_wire = MangoPay::PayIn::BankWire::Direct.create(AuthorId: user.mangopay_id,
                                                         CreditedUserId: user.mangopay_id,
                                                         CreditedWalletId: wallet.id,
                                                         DeclaredDebitedFunds: {
                                                           Currency: 'GBP',
                                                           Amount: amount
                                                         },
                                                         DeclaredFees: {
                                                           Currency: 'GBP',
                                                           Amount: 0
                                                         })

    raise MangoPay::Error, bank_wire['ResultMessage'] if bank_wire['Status'] == 'FAILED'

    log = PaymentLog.create_from_mangopay(user: user,
                                    mangopay_object: bank_wire,
                                    amount: amount,
                                    potential_investment_id: potential_investment_id)

    new(bank_wire, log.id)
  end

  def self.find_by_id(payment_id)
    new(MangoPay::PayIn.fetch(payment_id))
  end
end
