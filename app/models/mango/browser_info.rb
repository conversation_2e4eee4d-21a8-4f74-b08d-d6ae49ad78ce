# Additional mangopay data for 3DS2 integration.
# See: https://docs.mangopay.com/guide/3ds2-integration
class Mango::BrowserInfo
  attr_accessor :accept_header,
                :color_depth,
                :ip_address,
                :java_enabled,
                :javascript_enabled,
                :language,
                :screen_height,
                :screen_width,
                :time_zone_offset,
                :user_agent

  def initialize(params = {})
    params.each { |key, value| send "#{key}=", value }
  end

  def to_params
    {
      AcceptHeader: accept_header,
      ColorDepth: color_depth&.to_i,
      JavaEnabled: ActiveModel::Type::Boolean.new.cast(java_enabled),
      JavascriptEnabled: ActiveModel::Type::Boolean.new.cast(javascript_enabled),
      Language: language,
      ScreenHeight: screen_height&.to_i,
      ScreenWidth: screen_width&.to_i,
      TimeZoneOffset: time_zone_offset,
      UserAgent: user_agent
    }
  end
end
