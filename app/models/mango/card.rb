class Mango::Card
  attr_accessor :id, :alias, :card_type, :card_provider, :expiration_date,
                :country, :active, :validity, :owner_id

  def initialize(params = {})
    self.id = params['Id']
    self.alias = params['Alias']
    self.card_type = params['CardType']
    self.card_provider = params['CardProvider']
    self.expiration_date = params['ExpirationDate']
    self.country = params['Country']
    self.active = params['Active']
    self.validity = params['Validity']
    self.owner_id = params['UserId']
  end

  def self.find_by_id(card_id)
    new MangoPay::Card.fetch(card_id)
  end

  def self.deactivate(card_id)
    card = MangoPay::Card.update(card_id, Active: false)

    raise MangoPay::Error, 'Unable to deactivate card' if card['Active']

    new card
  end

  def self.payin_direct(user:, card_id:, amount:, return_url:, potential_investment_id: nil, browser_info: nil)
    card = Mango::Card.find_by_id(card_id)
    wallet = user.wallet

    raise 'Amount cannot be blank' if amount.blank?
    raise 'Wallet does not belong to that user' unless card && wallet && card.owner_id == user.mangopay_id

    amount = amount.to_i

    payin = MangoPay::PayIn::Card::Direct.create(AuthorId: user.mangopay_id,
                                                 BrowserInfo: browser_info.to_params,
                                                 CardId: card.id,
                                                 CreditedUserId: user.mangopay_id,
                                                 CreditedWalletId: wallet.id,
                                                 DebitedFunds: {
                                                   Amount: amount,
                                                   Currency: 'GBP'
                                                 },
                                                 Fees: {
                                                   Amount: 0,
                                                   Currency: 'GBP'
                                                 },
                                                 IpAddress: browser_info.ip_address,
                                                 SecureModeReturnURL: return_url)

    raise MangoPay::Secure3DError, payin['SecureModeRedirectURL'] if payin['SecureModeRedirectURL'].present?
    raise MangoPay::Error, payin['ResultMessage'] if payin['Status'] != 'SUCCEEDED'

    # Log the transaction and disable the card
    Mango::Card.log_and_disable(user, payin, potential_investment_id)

    payin
  end

  def self.confirm(user:, transaction_id:, potential_investment_id: nil)
    transaction = MangoPay::PayIn.fetch(transaction_id)

    raise MangoPay::Error, transaction['ResultMessage'] if transaction['Status'] != 'SUCCEEDED'

    log_and_disable(user, transaction, potential_investment_id)

    transaction
  end

  def self.log_and_disable(user, transaction, potential_investment_id = nil)
    # Disable the card once payment is complete
    Mango::Card.deactivate(transaction['CardId'])

    # Log the transaction
    PaymentLog.create_from_mangopay(user: user,
                                    mangopay_object: transaction,
                                    amount: transaction['CreditedFunds']['Amount'],
                                    potential_investment_id: potential_investment_id).successful!(transaction['Status'])
  end
end
