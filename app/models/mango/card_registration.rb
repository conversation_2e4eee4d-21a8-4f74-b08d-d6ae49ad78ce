class Mango::CardRegistration
  attr_accessor :id, :access_key, :card_registration_url, :pre_registration_data

  def initialize(params = {})
    self.id = params['Id']
    self.access_key = params['AccessKey']
    self.card_registration_url = params['CardRegistrationURL']
    self.pre_registration_data = params['PreregistrationData']
  end

  def self.create(user:)
    mangopay_card_registration = MangoPay::CardRegistration.create(userId: user.mangopay_id,
                                                                   currency: 'GBP')

    new(mangopay_card_registration)
  end
end
