class Mango::LegalUser
  ROOT_USER_ID = Rails.application.secrets.legal_user_id

  include Mango::Shared<PERSON><PERSON>

  def self.root
    user = User.find_by(email: '<EMAIL>')

    unless user
      user = User.new(first_name: 'uOWN',
                      last_name: 'Admin',
                      email: '<EMAIL>',
                      aasm_state: 'kyc_regular_is_complete')

      user.save(validate: false)
    end

    user
  end

  def self.kyc_light_params(user)
    {  Tag: user.id,
       Email: user.email,
       Name: user.business_name,
       LegalPersonType: user.legal_type,
       LegalRepresentativeFirstName: user.first_name,
       LegalRepresentativeLastName: user.last_name,
       LegalRepresentativeEmail: user.email,
       LegalRepresentativeBirthday: user.date_of_birth.to_datetime.to_i,
       LegalRepresentativeNationality: user.nationality,
       LegalRepresentativeCountryOfResidence: user.country_of_residence,
       TermsAndConditionsAccepted: true,
       LegalRepresentativeAddress: {
         AddressLine1: [user.address.address_number, user.address.address_1].join(' '),
         AddressLine2: user.address.address_2,
         City: user.address.city,
         Region: user.address.region,
         PostalCode: user.address.post_code,
         Country: user.address.country
       }
    }
  end

  def self.kyc_regular_params(user)
    {
      HeadquartersAddress: {
        AddressLine1: [user.headquarters.address_number, user.headquarters.address_1].join(' '),
        AddressLine2: user.headquarters.address_2,
        City: user.headquarters.city,
        Region: user.headquarters.region,
        PostalCode: user.headquarters.post_code,
        Country: user.headquarters.country
      }
    }
  end
end
