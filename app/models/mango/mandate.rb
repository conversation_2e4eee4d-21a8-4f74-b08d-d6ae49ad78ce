class Mango::Mandate
  LIMIT = 250_000 # 2500 EUR or 2500 GBP

  attr_accessor :id, :bank_account_id, :user_id, :return_url,
                :redirect_url, :document_url, :status

  # GOTCHA - bank_account_id is only returned when requesting a single
  # mandate. When requesting all a users mandates (i.e. find_by_user)
  # it is not returned.

  def initialize(params = {})
    self.id = params['Id']
    self.bank_account_id = params['BankAccountId']
    self.user_id = params['UserId']
    self.return_url = params['ReturnURL']
    self.redirect_url = params['RedirectURL']
    self.document_url = params['DocumentURL']
    self.status = params['Status']
  end

  def self.create(bank_account_id:, return_url:)
    mandate = MangoPay::Mandate.create(BankAccountId: bank_account_id,
                                       Culture: 'EN',
                                       ReturnURL: return_url)

    raise MangoPay::Error, mandate['ResultMessage'] if mandate['Status'] != 'CREATED'

    new(mandate)
  end

  def self.request_money(user:, amount:, mandate_id:)
    wallet = user.wallet

    payin = MangoPay::PayIn::DirectDebit::Direct.create(AuthorId: user.mangopay_id,
                                                        CreditedUserId: user.mangopay_id,
                                                        CreditedWalletId: wallet.id,
                                                        DebitedFunds: {
                                                          Currency: 'GBP',
                                                          Amount: amount
                                                        },
                                                        Fees: {
                                                          Currency: 'GBP',
                                                          Amount: 0
                                                        },
                                                        MandateId: mandate_id)

    raise MangoPay::Error, payin['ResultMessage'] if payin['Status'] != 'CREATED'

    PaymentLog.create_from_mangopay(user: user,
                                    mangopay_object: payin,
                                    amount: amount)

    payin
  end

  def self.find_by_id(mandate_id)
    new MangoPay::Mandate.fetch(mandate_id)
  end

  def self.cancel(mandate_id)
    new MangoPay::Mandate.cancel(mandate_id)
  end
end
