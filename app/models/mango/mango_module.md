# Module: mango

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
* `{Method1}`: {description}
* `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
* [Task1]: {brief description}
* [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
* [Task1]: {brief description}
* [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---

---KEY_DEFINITIONS_START---
Key Definitions:
2Gc#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango
2Gc1#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/KYC.rb
2Gc2#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/bank_account.rb
2Gc3#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/bank_wire.rb
2Gc4#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/browser_info.rb
2Gc5#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/card.rb
2Gc6#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/card_registration.rb
2Gc7#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/legal_user.rb
2Gc8#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/mandate.rb
2Gc9: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/natural_user.rb
2Gc10: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/payout.rb
2Gc11: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/shared_user.rb
2Gc12: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/temporary_wallet.rb
2Gc13: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/transfer.rb
2Gc14: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango/wallet.rb
---KEY_DEFINITIONS_END---

last_KEY_edit: Assigned keys: 2Gc, 2Gc1, 2Gc1, 2Gc2, 2Gc2, 2Gc3, 2Gc3, 2Gc4, 2Gc4, 2Gc5, 2Gc5, 2Gc6, 2Gc6, 2Gc7, 2Gc7, 2Gc8, 2Gc8, 2Gc9, 2Gc10, 2Gc11, 2Gc12, 2Gc13, 2Gc14
last_GRID_edit: Manual dep: wallet.rb -> [2Gc#1(mango), 2Gc1#1(KYC.rb), 2Gc10(payout.rb), 2Gc11(shared_user.rb), 2Gc12(temporary_wallet.rb), 2Gc13(transfer.rb), 2Gc2#1(bank_account.rb), 2Gc3#1(bank_wire.rb), 2Gc4#1(browser_info.rb), 2Gc5#1(card.rb), 2Gc6#1(card_registration.rb), 2Gc7#1(legal_user.rb), 2Gc8#1(mandate.rb), 2Gc9(natural_user.rb)] (n) (2025-06-10T13:06:16.299060)

---GRID_START---
X 2Gc#1 2Gc1#1 2Gc2#1 2Gc3#1 2Gc4#1 2Gc5#1 2Gc6#1 2Gc7#1 2Gc8#1 2Gc9 2Gc10 2Gc11 2Gc12 2Gc13 2Gc14
2Gc#1 = ox14
2Gc1#1 = non13
2Gc2#1 = nnon12
2Gc3#1 = n3on11
2Gc4#1 = n4on10
2Gc5#1 = n5o<n8
2Gc6#1 = n6on8
2Gc7#1 = n7on7
2Gc8#1 = n8on6
2Gc9 = n9on5
2Gc10 = n10on4
2Gc11 = n11on3
2Gc12 = n12on<
2Gc13 = n13on
2Gc14 = n14o
---GRID_END---

---mini_tracker_end---
