class Mango::NaturalUser
  include Mango::SharedUser

  def self.kyc_light_params(user)
    {  Tag: user.id,
       FirstName: user.first_name,
       LastName: user.last_name,
       Email: user.email,
       Birthday: user.date_of_birth.to_datetime.to_i,
       Nationality: user.nationality,
       CountryOfResidence: user.country_of_residence,
       UserCategory: 'OWNER',
       TermsAndConditionsAccepted: true,
       Address: {
         AddressLine1: [user.address.address_number, user.address.address_1].join(' '),
         AddressLine2: user.address.address_2,
         City: user.address.city,
         Region: user.address.region,
         PostalCode: user.address.post_code,
         Country: user.address.country
       }
    }
  end

  def self.kyc_regular_params(user)
    {
      IncomeRange: user.income_range,
      Occupation: user.occupation
    }
  end
end
