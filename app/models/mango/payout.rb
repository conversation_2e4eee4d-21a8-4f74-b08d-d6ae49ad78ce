class Mango::Payout
  attr_accessor :id, :amount, :bank_account_id, :status

  def initialize(params = {})
    self.id = params['Id']
    self.amount = params['CreditedFunds']['Amount'].to_i
    self.bank_account_id = params['BankAccountId']
    self.status = params['Status']
  end

  def self.create(user:, bank_account_id:, amount:)
    # The mangopay gem picks up that the bank account doesn't belong to the user
    # and throws an exception.  We catch that here but throw our own below
    # bank_account = Mango::BankAccount.find_by_id(user, bank_account_id) rescue nil
    # wallet = user.wallet
    #
    # raise 'User not completed KYC regular' unless user.kyc_regular_is_complete?
    # raise 'Amount cannot be blank' unless amount.present?
    # raise 'Bank account id is invalid or does not belong to that user' unless bank_account && wallet
    #
    # payout = MangoPay::PayOut::BankWire.create(AuthorId: user.mangopay_id,
    #                                            DebitedFunds: {
    #                                              Currency: 'GBP',
    #                                              Amount: amount
    #                                            },
    #                                            Fees: {
    #                                              Currency: 'GBP',
    #                                              Amount: 0
    #                                            },
    #                                            BankAccountId: bank_account.id,
    #                                            DebitedWalletId: wallet.id)
    #
    # raise MangoPay::Error, payout['ResultMessage'] if payout['Status'] != 'CREATED'
    #
    # PaymentLog.create_from_mangopay(user: user,
    #                                 mangopay_object: payout,
    #                                 amount: amount,
    #                                 direction: 'debit')
    #
    # new(payout)
  end

  def self.first_payout_bank?(user:, bank_account_id:)
    bank_account = Mango::BankAccount.find_by_id(user, bank_account_id) rescue nil
    return false if bank_account.blank?

    transactions = MangoPay::BankAccount.transactions(bank_account.id)
    transactions = transactions.select{|tr|tr['Type'] == 'PAYOUT'}
    transactions.count.zero?
  end
end
