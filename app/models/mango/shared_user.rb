module Mango::SharedUser
  extend ActiveSupport::Concern

  included do
    attr_accessor :id, :kyc_level, :creation_date

    def initialize(params = {})
      self.id = params['Id']
      self.kyc_level = params['KYCLevel']
      self.creation_date = params['CreationDate']
    end

    def self.update(user)
      params = kyc_light_params(user)
      params.merge!(kyc_regular_params(user)) if user.kyc_regular_is_pending?
      klass = user.instance_of?(User::Natural) ? MangoPay::NaturalUser : MangoPay::LegalUser

      mpu = if user.mangopay_id.present?
              klass.update(user.mangopay_id, params)
            else
              klass.create(params)
            end

      user.update_column(:mangopay_id, mpu['Id'])

      # Make sure the users default wallet gets created
      user.wallet if user.wallet_id.blank?

      if user.kyc_regular_is_pending?
        [:identity_proof, :registration_proof, :articles_of_association, :shareholder_declaration].each do |attribute|
          next unless user.send(attribute).present?

          already_passed = user.mangopay_kyc_documents.where(kind: attribute).validated.any?
          user.mangopay_kyc_documents.create!(kind: attribute, base_64_image: user.send(attribute)) unless already_passed
        end
      end

      new(mpu)
    end
  end
end
