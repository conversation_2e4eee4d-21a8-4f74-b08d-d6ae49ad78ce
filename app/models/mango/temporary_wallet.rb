class Mango::TemporaryWallet
  def self.create(property:, user:, amount:, fees:, identifier:)
    owner = Mango::LegalUser.root
    tag = "#{property.id}-#{user.mangopay_id}-#{identifier}"
    from_wallet = user.wallet

    temp_wallet = Mango::Wallet.create(user: owner,
                                       description: tag)

    transfer = Mango::Transfer.transfer(tag: tag,
                                        from_wallet: from_wallet,
                                        to_wallet: temp_wallet,
                                        amount: amount,
                                        fees: fees)

    [temp_wallet, transfer]
  end
end
