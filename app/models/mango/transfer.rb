class Mango::Transfer
  def self.transfer(tag:, from_wallet:, to_wallet:, amount:, fees: 0, idempotency_key: nil)
    transfer = MangoPay::Transfer.create({ Tag: tag,
                                           AuthorId: from_wallet.owner_id,
                                           CreditedUserId: to_wallet.owner_id,
                                           DebitedFunds: {
                                             Currency: 'GBP',
                                             Amount: amount
                                           },
                                           Fees: {
                                             Currency: 'GBP',
                                             Amount: fees
                                           },
                                           DebitedWalletId: from_wallet.id,
                                           CreditedWalletId: to_wallet.id },
                                         nil,
                                         idempotency_key)

    raise MangoPay::Error, transfer['ResultMessage'] if transfer['Status'] != 'SUCCEEDED'

    transfer
  end
end
