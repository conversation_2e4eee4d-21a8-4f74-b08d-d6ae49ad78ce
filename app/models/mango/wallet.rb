require 'mango_pay/secure_3d_error'

class Mango::Wallet
  DEFAULT_WALLET_NAME = 'uOwn User Wallet'.freeze

  attr_accessor :id, :description, :balance, :currency, :owner_id

  def initialize(params = {})
    self.id = params['Id']
    self.description = params['Description']
    self.balance = params['Balance']['Amount'].to_i
    self.currency = params['Balance']['Currency']
    self.owner_id = params['Owners'].first
  end

  def self.create(user:, description: DEFAULT_WALLET_NAME)
    wallet = MangoPay::Wallet.create(Owners: [user.mangopay_id],
                                     Currency: 'GBP',
                                     Description: description)
    new(wallet)
  end

  def self.find_by_user(user)
    MangoPay::User.wallets(user.mangopay_id).collect { |w| new(w) }
  end

  def self.find_by_id(wallet_id)
    begin
      new MangoPay::Wallet.fetch(wallet_id)
    rescue MangoPay::ResponseError => error
        puts "Failed to fetch wallet: #{error.message}"
        puts "Error details: #{error.details}"
        return {}
    end
  end
end
