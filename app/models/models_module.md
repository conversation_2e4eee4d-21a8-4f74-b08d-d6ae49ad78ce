# Module: models

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
* `{Method1}`: {description}
* `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
* [Task1]: {brief description}
* [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
* [Task1]: {brief description}
* [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---

---KEY_DEFINITIONS_START---
Key Definitions:
2G#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models
2G1#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/.keep
2G2#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/admin_notification.rb
2G3#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/certification.rb
2G4#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/investment_document.rb
2G5#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mandate.rb
2G6#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/payment_log.rb
2G7#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/potential_investment.rb
2G8#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/potential_investment_item.rb
2G9#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property.rb
2G10#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property_certification_level.rb
2G11: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/remote_bank_account.rb
2G12: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/role.rb
2G13: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/share.rb
2G14: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user.rb
2G15: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user_role.rb
2Ga#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/certification
2Gb#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/concerns
2Gc#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/mango
2Gd#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property
2Ge: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/share
2Gf: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user
---KEY_DEFINITIONS_END---

last_KEY_edit: Assigned keys: 2G, 2G1, 2G1, 2G2, 2G2, 2G3, 2G3, 2G4, 2G4, 2G5, 2G5, 2G6, 2G6, 2G7, 2G7, 2G8, 2G8, 2G9, 2G9, 2G10, 2G10, 2G11, 2G12, 2G13, 2G14, 2G15, 2Ga, 2Gb, 2Gc, 2Gd, 2Ge, 2Gf
last_GRID_edit: Manual dep: property.rb -> [2G1#1(.keep), 2G10#1(property_certification_level.rb), 2G11(remote_bank_account.rb), 2G12(role.rb), 2G13(share.rb), 2G14(user.rb), 2G15(user_role.rb), 2G2#1(admin_notification.rb), 2G3#1(certification.rb), 2G4#1(investment_document.rb), 2G5#1(mandate.rb), 2G6#1(payment_log.rb), 2G7#1(potential_investment.rb), 2G8#1(potential_investment_item.rb), 2Ga#1(certification), 2Gc#1(mango), 2Ge(share), 2Gf(user)] (n) (2025-06-10T10:24:31.174382)

---GRID_START---
X 2G#1 2G1#1 2G2#1 2G3#1 2G4#1 2G5#1 2G6#1 2G7#1 2G8#1 2G9#1 2G10#1 2G11 2G12 2G13 2G14 2G15 2Ga#1 2Gb#1 2Gc#1 2Gd#1 2Ge 2Gf
2G#1 = ox21
2G1#1 = xon20
2G2#1 = xnon14<n4
2G3#1 = xnnon12<<n4
2G4#1 = xn3on12<n4
2G5#1 = xn4on11<n4
2G6#1 = xn5on10<n4
2G7#1 = xn6on9<n4
2G8#1 = xn7on8<n4
2G9#1 = xn8on7<n<nn
2G10#1 = xn9on6<n4
2G11 = xn10on5<n4
2G12 = xn11on4<n4
2G13 = xn12on3<nn<n
2G14 = xn13onn<n3<
2G15 = xn14on<n4
2Ga#1 = xnn>n12on5
2Gb#1 = xn16on4
2Gc#1 = xn17on3
2Gd#1 = xn8>n9onn
2Ge = xn12>n6on
2Gf = xn13>n6o
---GRID_END---

---mini_tracker_end---
