class PaymentLog < ActiveRecord::Base
  DIRECTIONS = %w(debit credit).freeze

  belongs_to :user
  belongs_to :dividend, class_name: 'Property::Dividend', optional: true
  belongs_to :potential_investment, optional: true

  has_one :queued_action, class_name: 'User::QueuedAction', as: :source

  validates :user, :remote_id, :kind, :status, presence: true
  validates :direction, presence: true, inclusion: { in: DIRECTIONS }

  after_create :create_queued_action
  after_save :check_kyc_limitations

  scope :successful, -> { where.not(successful_at: nil).where(failed_at: nil) }

  def self.create_from_remote(user:, remote_object:, amount:, direction: 'credit', potential_investment_id: nil)
    PaymentLog.create(amount: amount,
                      direction: direction,
                      kind: 'NuaPay',
                      remote_id: remote_object['id'],
                      potential_investment_id: potential_investment_id,
                      status: remote_object['status'],
                      user: user)
  end

  def successful!(status)
    self.status = status
    self.successful_at = Time.now
    save(validate: false)

    process_queued_action!
  end

  def failure!(status)
    self.status = status
    self.failed_at = Time.now
    save(validate: false)

    fail_queued_action!
    send_admin_notification
  end

  def check_kyc_limitations
    user.check_kyc_limitations!
  end

  def pending?
    successful_at.blank? && failed_at.blank?
  end

  private

  def create_queued_action
    return unless potential_investment

    User::QueuedActionInvestment.create!(source: self,
                                         target: potential_investment,
                                         user: user)
  end

  def process_queued_action!
    return unless queued_action

    queued_action&.process! unless user.kyc_regular_is_pending?
  end

  def fail_queued_action!
    return unless queued_action

    queued_action&.fail_with_message('Investment Payment failed')
  end

  def send_admin_notification
    AdminNotification.payment_failure(self)
  end
end
