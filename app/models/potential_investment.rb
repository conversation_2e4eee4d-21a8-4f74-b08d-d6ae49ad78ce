class PotentialInvestment < ActiveRecord::Base
  belongs_to :user
  has_many :payment_logs, dependent: :nullify
  has_many :items, inverse_of: :potential_investment,
                   class_name: 'PotentialInvestmentItem',
                   dependent: :destroy
  has_many :properties, through: :items

  accepts_nested_attributes_for :items, allow_destroy: true, reject_if: :all_blank

  validates :user, presence: true

  def share_orders
    @share_orders ||= items.collect(&:buy_order)
    @share_orders.each(&:set_total_amount)
    @share_orders
  end

  def invest!
    raise 'Invalid share order/s' if share_orders.collect(&:valid?).include?(false)

    share_orders.each(&:save!)
  end

  def total
    share_orders.sum(&:total_with_fees)
  end
end
