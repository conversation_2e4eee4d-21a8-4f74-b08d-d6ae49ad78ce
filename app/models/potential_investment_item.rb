class PotentialInvestmentItem < ActiveRecord::Base
  belongs_to :potential_investment, inverse_of: :items
  belongs_to :property

  validates :potential_investment, :property, :quantity, presence: true
  validate :validate_buy_order

  def buy_order
    @buy_order ||= Share::BuyOrder.new(property: property, quantity: quantity, user: potential_investment.user)
  end

  private

  def validate_buy_order
    return unless property && quantity && potential_investment

    return if buy_order.valid?

    buy_order.errors[:quantity].each do |error|
      errors.add(:quantity, error)
    end

    buy_order.errors[:property].each do |error|
      errors.add(:property, error)
    end
  end
end
