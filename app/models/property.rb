class Property < ActiveRecord::Base
  DECIMAL_PLACES = 2

  has_many :buy_orders,      class_name: 'Share::BuyOrder'
  has_many :documents,       class_name: 'Property::Document',       dependent: :destroy
  has_many :floorplans,      class_name: 'Property::Floorplan',      dependent: :destroy
  has_many :photos,          class_name: 'Property::Photo',          dependent: :destroy
  has_many :legal_documents, class_name: 'Property::LegalDocument',  dependent: :destroy
  has_many :news_items,      class_name: 'Property::NewsItem',       dependent: :destroy
  has_many :sell_orders,     class_name: 'Share::SellOrder'
  has_many :share_orders,    class_name: 'Share::Order'
  has_many :share_logs,      class_name: 'Share::Log'

  has_many :property_certification_levels, dependent: :destroy
  has_many :certification_levels, through: :property_certification_levels

  has_many :potential_investment_items, dependent: :destroy
  has_many :potential_investments, through: :potential_investment_items
  has_many :payment_logs, through: :potential_investments

  has_many :property_tags, dependent: :destroy
  has_many :tags, through: :property_tags
  has_one :remote_bank_account, as: :bankable, dependent: :destroy

  scope :visible, -> { where(visible: true) }

  validates :name, :slug, :share_count, presence: true
  validates :name, :slug, uniqueness: true

  accepts_nested_attributes_for :documents,       allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :floorplans,      allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :legal_documents, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :photos,          allow_destroy: true, reject_if: :all_blank

  before_validation :generate_slug
  after_create :create_nuapay_account, unless: :placeholder?

  def target_amount
    # To be overridden in subclasses
    property_amount
  end

  def available_shares
    orders = sell_orders.active
    total = orders.sum(:quantity)
    issued = Share::OrderTransaction.where(order_id: orders.ids).sum(:quantity)

    total - issued
  end


  def wallet
    Mango::Wallet.find_by_id(wallet_id)
  end

  def share_price
    (target_amount.to_d / share_count).round(DECIMAL_PLACES)
  end

  def upgrade_from_placeholder!
    return update!(placeholder: false, funded: false) if create_nuapay_account

    false
  end

  private

  def generate_slug
    return unless name.present?

    self.slug = name.parameterize
  end

  def create_nuapay_account
    user = User.admin_uown
    # Create the required share order
    sell_order = Share::SellOrder.create!(user: user,
                                          property: self,
                                          quantity: share_count)
    # Create the corresponding share log
    Share::Log.create!(buy_order: sell_order,
                       sell_order: sell_order,
                       user: user,
                       property: self,
                       quantity: share_count)
    params = { name: slug, currency: "GBP", type: "CURRENT_ACCOUNT" }
    account = ::Nuapay::Accounts::CreateService.new(params: params).call
    raise "#{account.error}" unless account.success?
    acc_res = account.body['data']
    remote_bank_account = build_remote_bank_account(owner_name: acc_res['name'], remote_id: acc_res['id'], status: acc_res['status'])
    remote_bank_account.save(validate: false)
  end
end
