class Property::Cause < Property
  CURRENCY_ATTRIBUTES = [:property_amount].freeze

  def target_amount
    property_amount
  end

  def unique_contributors
    share_logs.where.not(user_id: User.admin_uown.id)
              .pluck(:user_id)
              .uniq
              .count
  end

  # Currency Helpers
  CURRENCY_ATTRIBUTES.each do |attribute|
    validates attribute, presence: true, numericality: true

    define_method "#{attribute}_in_pounds=" do |value|
      send("#{attribute}=", (value.to_d * 100)).to_i
    end

    define_method "#{attribute}_in_pounds" do
      send(attribute).to_d / 100 rescue 0
    end
  end
end
