class Property::Dividend < ActiveRecord::Base
  include Idempoten<PERSON><PERSON><PERSON>

  belongs_to :payout
  belongs_to :property
  belongs_to :user

  has_one :remote_bank_account, through: :user, source: :remote_bank_account
  delegate :property, to: :payout, allow_nil: true
  has_one :property_bank_account, through: :property, source: :remote_bank_account


  has_many :payment_logs

  include AASM

  aasm do
    state :pending, initial: true
    state :complete
    state :failed
    state :deferred

    event :complete do
      transitions from: [:pending, :deferred], to: :complete, success: :check_kyc_limitations
    end

    event :failed do
      transitions from: [:pending, :deferred], to: :failed
    end

    event :deferred do
      transitions from: :pending, to: :deferred
    end
  end

  validates :property, :payout, :user, presence: true

  def queue!
    # DividendJob.perform_later(id: id) if pending?
  end

  def amount_to_pay(as_of_payout_date: false)
    (amount + outstanding_amount_as_of_payout_date(as_of_payout_date)).floor
  end

  def outstanding_amount_as_of_payout_date(as_of_payout_date)
  payouts= Property::Payout.where(property_id: payout.property_id)
  payout_ids = ( as_of_payout_date ? payouts.where('created_at <= ?', payout.created_at) : payouts ).ids

    previous_dividends = user.dividends
                             .where(aasm_state: ['complete', 'failed', 'deferred'])
                             .where(payout_id: payout_ids)
                             .where.not(id: id)

    total_owed = previous_dividends.sum(:amount)
    total_paid = user.payment_logs
                     .where(kind: 'DIVIDEND', dividend_id: previous_dividends.ids)
                     .successful
                     .sum(:amount)


    total_owed - total_paid
  end
  def send_receipt!
    return unless amount_to_pay.positive?

    if payout.is_a?(Property::PayoutProperty)
      if property.is_a?(Property::Development)
        PropertyDividendDelivery.notify(:development, self)
      elsif property.is_a?(Property::Loan)
        PropertyDividendDelivery.notify(:loan, self)
      else
        PropertyDividendDelivery.notify(:property, self)
      end
    elsif property.is_a?(Property::Loan)
      PropertyDividendDelivery.notify(:interest, self)
    else
      PropertyDividendDelivery.notify(:rent, self)
    end
  end

  def check_kyc_limitations
    user.kyc_regular_is_complete?
  end

  def mark_as_deferred!
    deferred_reason = []
    deferred_reason << "Awaiting KYC verification"  unless user.kyc_regular_is_complete?
    deferred_reason << "Missing or incomplete bank account details"  unless user.remote_bank_account&.valid_bank?

    self.reason = deferred_reason.join(', ')
    deferred!
  end
end
