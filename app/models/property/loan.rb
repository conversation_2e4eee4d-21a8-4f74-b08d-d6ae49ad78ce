class Property::Loan < Property
  include PropertyCommonValidation
  include PropertyLabels

  CURRENCY_ATTRIBUTES = [:finance,
                         :gdv,
                         :profit,
                         :property_amount,
                         :property_fee_legal_and_professional,
                         :site_value].freeze

  has_one_attached :acknowledgement_pdf

  validates :acknowledgement_pdf, content_type: 'application/pdf'
  validates :annualised_return, :estimated_completion_date, presence: true
  validates :term, numericality: true

  def target_amount
    property_amount +
    property_fee_legal_and_professional +
    site_value -
    finance
  end

  def available_shares
    return 0 if funded?
    super
  end

  def return_percentage
    (profit.to_d / target_amount) * 100
  end

  # Currency Helpers
  CURRENCY_ATTRIBUTES.each do |attribute|
    validates attribute, presence: true, numericality: true

    define_method "#{attribute}_in_pounds=" do |value|
      send("#{attribute}=", (value.to_d * 100)).to_i
    end

    define_method "#{attribute}_in_pounds" do
      send(attribute).to_d / 100 rescue 0
    end
  end
end
