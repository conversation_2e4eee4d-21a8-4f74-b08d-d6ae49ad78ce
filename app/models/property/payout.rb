class Property::Payout < ActiveRecord::Base
  belongs_to :property
  belongs_to :user

  has_many :dividends, dependent: :restrict_with_error
  has_many :fees, class_name: 'Property::PayoutFee', inverse_of: :payout, dependent: :destroy
  has_many :exit_orders, class_name: 'Share::ExitOrder', dependent: :restrict_with_error
  has_one :property_bank_account, through: :property, source: :remote_bank_account

  include AmountInPounds
  include PropertyDividendCalculations
  include AASM

  aasm do
    state :pending, initial: true
    state :funds_allocated
    state :fees_transferred
    state :dividends_distributed

    event :allocate_funds do
      transitions from: :pending, to: :funds_allocated, guard: :transfer_funds!
    end

    event :transfer_fees do
      transitions from: :funds_allocated, to: :fees_transferred, guard: :transfer_fees_to_property_wallet!
    end

    event :distribute_dividends, after_commit: :distribute! do
      transitions from: :fees_transferred, to: :dividends_distributed
    end
  end

  validates :amount, :property, :user, presence: true
  validates :amount_in_pounds, numericality: { less_than_or_equal_to: 100_000 }
  validate :check_subtotal_positive

  accepts_nested_attributes_for :fees, allow_destroy: true, reject_if: :all_blank

  # after_create :create_wallet
  after_create :create_dividends

  ## initalization

  def build_default_fees
    self.class.default_fees.each do |item|
      fees.build(description: item)
    end
  end

  ## validation

  def check_subtotal_positive
    errors.add(:amount_in_pounds, 'subtotal cannot be negative') if amount && subtotal.negative?
  end

  ## helpers

  def wallet
    # Mango::Wallet.find_by_id(wallet_id)
  end

  ## allocate_funds!

  def funds_available?
    # account = property_bank_account
    # return false if account.blank? || account.invalid_bank? || account.remote_id.blank?

    uown_balance = Nuapay::Accounts::BalanceService.new(account_id:'abx6anzdbl').call
    return flase unless uown_balance.success?
    available_balance = uown_balance.body['data'].sum do |balance|
      balance['balance']['type']=='AVAILABLE_BALANCE' ? balance['balance']['amount'] : 0
    end
    allocation_amount <= available_balance.to_d * 100
  end

  def transfer_funds!
    return true if funds_available?

    errors.add(:amount_in_pounds, " #{property.name} does not have enough funds available")
    false
    # Mango::Transfer.transfer(tag: "payout-#{id}",
    #                          from_wallet: Mango::LegalUser.root.wallet,
    #                          to_wallet: wallet,
    #                          amount: allocation_amount)
  end

  ## transfer_fees!

  def transfer_fees_to_property_wallet!
    true
    # return true if total_fees.zero?

    # Mango::Transfer.transfer(tag: "payout-fees-#{id}",
    #                          from_wallet: wallet,
    #                          to_wallet: property.wallet,
    #                          amount: total_fees)
  end

  ## distribute_dividends!

  def distribute!
    exit_orders.each(&:activate!)
    res = Nuapay::CreditTransfers::Batch::ProcessDividendsService.new(dividend_ids: dividends.ids).call
    raise res.error unless res.success?
    # dividends.each(&:queue!)
  end

  def self.default_fees
    raise 'Must be overridden by subclass'
  end

  private

  ## after_create

  def create_wallet
    return if wallet_id.present?

    wallet = Mango::Wallet.create(user: Mango::LegalUser.root,
                                  description: "payout-#{id}-wallet")

    update_attribute(:wallet_id, wallet.id)
  end

  ## after_create

  def create_dividends
    return dividends if dividends.any?

    transaction do
      grouped_dividends.each do |dividend|
        dividends.create!(user: dividend[:user],
                          property: property,
                          amount: dividend[:amount])
      end
    end

    dividends
  end

  def grouped_dividends
    raise 'Must be overridden by subclass'
  end

  def grouped_shares_on(date = Date.today)
    end_of_day = date.to_datetime.end_of_day

    ::Share::Log.select('sum(share_logs.quantity) as total,
                         max(share_logs.id),
                         share_logs.user_id,
                         share_logs.property_id')
                .where('share_logs.created_at < ?', end_of_day)
                .where(property_id: property.id)
                .group(:user_id)
  end
end
