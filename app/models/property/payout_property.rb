class Property::PayoutProperty < Property::Payout
  after_create :create_exit_orders

  validate :check_property_type, on: :create
  validate :check_funded, on: :create
  validate :check_active_sell_orders, on: :create

  def self.default_fees
    []
  end

  def grouped_dividends
    grouped_shares_on.collect { |sl| { user: sl.user, amount: total_per_share * sl.total } }
  end

  def return_percentage
    # Loans should take into account any interest payments
    if property.is_a?(Property::Loan)
      interest = Property::PayoutRent.where(property: property).sum(:amount)

      ((amount.to_d + interest) / property.target_amount) * 100
    else
      (amount.to_d / property.target_amount) * 100
    end
  end

  private

  def create_exit_orders
    return exit_orders if exit_orders.any?

    transaction do
      grouped_shares_on.each do |sl|
        # Can't transfer shares from the root user to the root user
        next if sl.user == User.admin_uown

        # Exit if they've already sold all their shares
        next if sl.total.zero?

        exit_orders.create!(user: sl.user,
                            quantity: sl.total,
                            property: property)
      end
    end

    exit_orders
  end

  def check_property_type
    return unless property

    errors.add(:property_id, "can't be a cause") if property.is_a?(Property::Cause)
  end

  def check_funded
    return unless property

    errors.add(:property_id, 'must be marked as funded') unless property.funded?
  end

  def check_active_sell_orders
    return unless property

    sell_order_count = property.sell_orders
                               .active
                               .where.not(user: User.admin_uown)
                               .count

    errors.add(:property_id, "has #{sell_order_count} active sell orders, please cancel them first") if sell_order_count.positive?
  end
end
