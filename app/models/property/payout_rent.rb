class Property::PayoutRent < Property::Payout
  validates :start_date, :end_date, presence: true
  validate :check_start_end_dates

  ## validation

  def check_start_end_dates
    errors.add(:start_date, 'must be before today') if start_date && start_date > Time.zone.today
    errors.add(:end_date, 'must be before today') if end_date && end_date > Time.zone.today

    return unless start_date && end_date

    errors.add(:start_date, 'cannot be the same as the end date') if start_date == end_date

    if end_date < start_date
      errors.add(:end_date, 'must be after the start date')
    else
      return unless property && new_record?

      ::Property::PayoutRent.where(property: property).find_each do |old_payment|
        next unless old_payment.date_range.overlaps?(date_range)

        errors.add(:start_date, 'clashes with another rent payment')
        break
      end
    end
  end

  ## helpers

  def date_range
    start_date..end_date
  end

  def self.default_fees
    ['Property Management Fees',
     'Property Insurance',
     'Allowance for possible voids',
     'Maintenance allowance',
     'Corporation Tax',
     'uOwn annual service Fee']
  end

  def grouped_dividends
    grouped = []

    calculated_dividends.each do |dividend|
      selected = grouped.find { |g| g[:user] == dividend[:user] }

      if selected
        selected[:amount] += dividend[:amount]
      else
        grouped << {
          user: dividend[:user],
          amount: dividend[:amount]
        }
      end
    end

    grouped
  end

  def calculated_dividends
    dividends = []
    total_per_share_per_day = total_per_share / date_range.to_a.size

    date_range.each do |date|
      grouped_shares_on(date).each do |share_log|
        dividends << {
          user: share_log.user,
          quantity: share_log.total,
          amount: total_per_share_per_day * share_log.total,
          date: date
        }
      end
    end

    dividends
  end
end
