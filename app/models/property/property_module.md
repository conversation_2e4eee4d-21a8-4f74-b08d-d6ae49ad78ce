# Module: property

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
* `{Method1}`: {description}
* `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
* [Task1]: {brief description}
* [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
* [Task1]: {brief description}
* [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---

---KEY_DEFINITIONS_START---
Key Definitions:
2Gd#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property
2Gd1#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/cause.rb
2Gd2#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/development.rb
2Gd3#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/dividend.rb
2Gd4#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/document.rb
2Gd5#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/floorplan.rb
2Gd6#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/legal_document.rb
2Gd7#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/loan.rb
2Gd8#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/news_item.rb
2Gd9#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/payout.rb
2Gd10#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/payout_fee.rb
2Gd11#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/payout_property.rb
2Gd12: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/payout_rent.rb
2Gd13: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/photo.rb
2Gd14: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/property_tag.rb
2Gd15: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/regular.rb
2Gd16: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/property/tag.rb
---KEY_DEFINITIONS_END---

last_KEY_edit: Assigned keys: 2Gd, 2Gd1, 2Gd1, 2Gd2, 2Gd2, 2Gd3, 2Gd3, 2Gd4, 2Gd4, 2Gd5, 2Gd5, 2Gd6, 2Gd6, 2Gd7, 2Gd7, 2Gd8, 2Gd8, 2Gd9, 2Gd9, 2Gd10, 2Gd10, 2Gd11, 2Gd11, 2Gd12, 2Gd13, 2Gd14, 2Gd15, 2Gd16
last_GRID_edit: Manual dep: property_tag.rb -> [2Gd#1(property), 2Gd1#1(cause.rb), 2Gd10#1(payout_fee.rb), 2Gd11#1(payout_property.rb), 2Gd12(payout_rent.rb), 2Gd13(photo.rb), 2Gd15(regular.rb), 2Gd2#1(development.rb), 2Gd3#1(dividend.rb), 2Gd4#1(document.rb), 2Gd5#1(floorplan.rb), 2Gd6#1(legal_document.rb), 2Gd7#1(loan.rb), 2Gd8#1(news_item.rb), 2Gd9#1(payout.rb)] (n) (2025-06-10T13:48:04.649060)

---GRID_START---
X 2Gd#1 2Gd1#1 2Gd2#1 2Gd3#1 2Gd4#1 2Gd5#1 2Gd6#1 2Gd7#1 2Gd8#1 2Gd9#1 2Gd10#1 2Gd11#1 2Gd12 2Gd13 2Gd14 2Gd15 2Gd16
2Gd#1 = ox16
2Gd1#1 = non15
2Gd2#1 = nnon14
2Gd3#1 = n3on5<n7
2Gd4#1 = n4on>n10
2Gd5#1 = n5on11
2Gd6#1 = n4<non10
2Gd7#1 = n7on9
2Gd8#1 = n8on8
2Gd9#1 = n3>n5o>3n4
2Gd10#1 = n9<on6
2Gd11#1 = n9<non5
2Gd12 = n9<nnon4
2Gd13 = n13on3
2Gd14 = n14on<
2Gd15 = n15on
2Gd16 = n14>no
---GRID_END---

---mini_tracker_end---
