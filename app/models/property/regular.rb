class Property::Regular < Property
  include PropertyCommonValidation

  CURRENCY_ATTRIBUTES = [:property_amount,
                         :rent_amount,
                         :property_fee_stamp_duty,
                         :property_fee_legal_and_professional,
                         :property_fee_pre_let_expenses,
                         :property_fee_repairs_provision,
                         :property_fee_deferred_tax,
                         :rental_fee_management,
                         :rental_fee_insurance,
                         :rental_fee_allowance_for_voids,
                         :rental_fee_maintenance_allowance,
                         :rental_fee_corporation_tax,
                         :rental_fee_deferred_fees,
                         :rental_fee_spv_charge].freeze

  validates :hpi, :hpi_area, presence: true

  validate :check_property_mode

  def target_amount
    property_amount +
    property_fee_stamp_duty +
    property_fee_legal_and_professional +
    property_fee_pre_let_expenses +
    property_fee_repairs_provision -
    property_fee_deferred_tax
  end

  # Currency Helpers
  CURRENCY_ATTRIBUTES.each do |attribute|
    validates attribute, presence: true, numericality: true

    define_method "#{attribute}_in_pounds=" do |value|
      send("#{attribute}=", (value.to_d * 100)).to_i
    end

    define_method "#{attribute}_in_pounds" do
      send(attribute).to_d / 100 rescue 0
    end
  end

  private

  def check_property_mode
    return unless funded? && easy_exit?

    message = 'Property cannot be funded and instant exit'

    errors.add(:funded, message)
    errors.add(:easy_exit, message)
  end
end
