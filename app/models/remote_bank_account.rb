require 'ibandit'
class RemoteBankAccount  < ActiveRecord::Base
  alias_attribute 'sort_code', 'branch_code'
  belongs_to :bankable, polymorphic: true
  validates :account_number, length: { is: 8 }, if: -> { account_number.present? }
  validates :sort_code, length: { is: 6 }, if: -> { sort_code.present? }
  validates :iban, length: { is: 22 }, if: -> { iban.present? }
  validate :iban_or_account_details_present


  def valid_bank?
    return true if iban.present? && status == 'ACTIVE'
    return true if sort_code.present? && account_number.present? && status == 'ACTIVE'
    false
  end

  def invalid_bank?
    !valid_bank?
  end

  def sort_code_account_number
    if sort_code.present? && account_number.present?
    "#{sort_code}#{account_number}"
    elsif iban.present?
      ibandit = Ibandit::IBAN.new(iban)
      "#{ibandit.swift_branch_code}#{ibandit.swift_account_number}"
    end
  end

  def iban_or_account_details_present
    if iban.blank? && (account_number.blank? || sort_code.blank?)
      errors.add(:base, 'Either IBAN must be present, or both account number and sort code must be present')
    end

    if iban.present?
      ibandit = Ibandit::IBAN.new(iban)
      errors.add(:iban, ibandit.errors&.values&.to_sentence) unless ibandit.valid?
    end
  end

  def sort_code
    if self[:sort_code].present?
      self[:sort_code]
    elsif iban.present?
      ibandit = Ibandit::IBAN.new(iban)
      ibandit.swift_branch_code
    end
  end

  def account_number
    if self[:account_number].present?
      self[:account_number]
    elsif iban.present?
      ibandit = Ibandit::IBAN.new(iban)
      ibandit.swift_account_number
    end
  end
end
