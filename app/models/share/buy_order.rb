class Share::BuyOrder < Share::Order
  FEE_PERCENTAGE = 0.02 # 2%
  STAMP_DUTY_PERCENTAGE = 0.005 # 0.5%

  aasm do
    state :pending, initial: true
    state :funds_allocated
    state :completed
    state :rejected

    event :allocate_funds do
      transitions from: :pending, to: :funds_allocated
    end

    event :complete do
      transitions from: :funds_allocated, to: :completed, after: :send_receipt
    end

    event :reject do
      transitions to: :rejected
    end
  end

  validate :validate_shares_available, on: :create

  after_create :check_and_transfer_funds
  after_commit :queue!, on: :create

  def check_and_transfer_funds
    allocate_funds!
  rescue StandardError => e
    reject_with_reason(e.message)
  end

  def queue!
    ShareOrderJob.perform_later(id: id) if funds_allocated?
  end

  def calculated_fees
    transaction_fee + stamp_duty
  end

  def transaction_fee
    (total_amount * FEE_PERCENTAGE).ceil
  end

  def stamp_duty
    return 0 unless property.funded?

    (total_amount * STAMP_DUTY_PERCENTAGE).ceil
  end

  def total_with_fees
    total_amount + calculated_fees
  end

  def send_receipt
    if property.is_a?(Property::Cause)
      ShareBuyOrderDelivery.notify(:contributed, self)
      AdminNotification.cause_contribution(self)
    elsif property.is_a?(Property::Loan)
      ShareBuyOrderDelivery.notify(:loan, self)
      AdminNotification.shares_purchased(self)
    else
      ShareBuyOrderDelivery.notify(:bought, self)
      AdminNotification.shares_purchased(self)
    end

    send_admin_notification
  rescue => e
    Rails.logger.error("Unable to send Share::BuyOrder receipt. #{id} / #{e.message}")
  end

  private

  def validate_shares_available
    return unless quantity && property
    return if quantity <= property.available_shares

    errors.add(:quantity, I18n.t('share_buy_orders.available', available: property.available_shares))
  end
end
