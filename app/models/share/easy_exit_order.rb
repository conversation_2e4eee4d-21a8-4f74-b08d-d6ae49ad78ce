class Share::EasyExitOrder < Share::Order
  FEE_PERCENTAGE = 0.03 # 3%

  include SharedOrder

  aasm do
    state :pending, initial: true
    state :active
    state :completed
    state :rejected
    state :cancelled

    event :activate, after_commit: :queue! do
      transitions from: :pending, to: :active
    end

    event :complete, after_commit: :relist! do
      transitions from: :active, to: :completed, after: :send_receipt
    end

    event :reject do
      transitions to: :rejected, after: :send_rejection_receipt
    end

    event :cancel do
      transitions to: :cancelled, guard: :mark_cancelled
    end
  end

  after_create :send_notifications

  def queue!
    ShareOrderJob.perform_later(id: id)
  end

  def queue_cancellation!
    CancelOrderJob.perform_later(id: id) if pending?
  end

  def total_before_fees
    (property.share_price * quantity).floor
  end

  def calculated_fees
    (total_before_fees * FEE_PERCENTAGE).ceil
  end

  def total_with_fees
    total_before_fees - calculated_fees
  end

  def set_total_amount
    self.total_amount = total_with_fees
  end

  def relist!
    # Overridden in transaction processor
    false
  end

  private

  def send_receipt
    ShareEasyExitOrderDelivery.notify(:completed, self)
  rescue => e
    Rails.logger.error("Unable to send Share::EasyExitOrder receipt. #{id} / #{e.message}")
  end

  def send_rejection_receipt
    ShareEasyExitOrderDelivery.notify(:rejected, self)
  rescue => e
    Rails.logger.error("Unable to send Share::EasyExitOrder rejection receipt. #{id} / #{e.message}")
  end

  def send_notifications
    ShareEasyExitOrderDelivery.notify(:created, self)
    AdminNotification.easy_exit_order(self)
  rescue => e
    Rails.logger.error("Unable to send Share::EasyExitOrder confirmation. #{id} / #{e.message}")
  end
end
