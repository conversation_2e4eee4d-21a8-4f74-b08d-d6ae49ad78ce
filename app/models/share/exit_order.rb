class Share::ExitOrder < Share::Order
  include SharedOrder

  aasm do
    state :pending, initial: true
    state :active
    state :completed
    state :rejected
    state :cancelled

    event :activate, after_commit: :queue! do
      transitions from: :pending, to: :active
    end

    event :complete do
      transitions from: :active, to: :completed
    end

    event :reject do
      transitions to: :rejected
    end

    event :cancel do
      transitions to: :cancelled, guard: :mark_cancelled
    end
  end

  def queue!
    ShareOrderJob.perform_later(id: id)
  end

  # There are no fees but we need to set them to share logic elsewhere

  def total_before_fees
    total_amount
  end

  def total_with_fees
    total_before_fees
  end
end
