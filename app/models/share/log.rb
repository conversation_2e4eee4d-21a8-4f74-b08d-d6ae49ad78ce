class Share::Log < ActiveRecord::Base
  belongs_to :user, class_name: 'User'
  belongs_to :property, class_name: 'Property'
  belongs_to :buy_order, class_name: 'Share::Order'
  belongs_to :sell_order, class_name: 'Share::Order'

  validates :property, :user, :buy_order, :sell_order, presence: true
  validates :quantity, numericality: true, presence: true

  before_save :set_total_amount
  after_save :sync_user_to_flg_crm

  private

  def set_total_amount
    return unless total_amount.zero?

    self.total_amount = (property.share_price * quantity).floor
  end

  def sync_user_to_flg_crm
    return if self.user.registered?
    return unless Rails.env.production?

    SyncFlgToCrmJob.perform_later(self.user.id)
  end
end
