class Share::Order < ActiveRecord::Base
  self.ignored_columns = [:wallet_id] # Just ignoring, in future we ll remove this column
  belongs_to :user, class_name: 'User'
  belongs_to :property, class_name: 'Property'
  belongs_to :payout, optional: true, class_name: 'Property::Payout'

  has_many :order_transactions

  include AASM

  validates :user, :property, presence: true
  validates :quantity, numericality: { greater_than: 0, only_integer: true }, presence: true

  before_save :set_total_amount
  before_save :set_total_fees

  after_save :check_kyc_limitations

  def quantity_remaining
    remaining = (quantity || 0) - order_transactions.sum(:quantity)
    raise 'Quantity remaining is less than zero' if remaining < 0
    remaining
  end

  # Overridden by subclasses
  def calculated_fees
    0
  end

  def set_total_amount
    self.total_amount = (property.share_price * quantity).floor
  end

  def set_total_fees
    self.total_fees = calculated_fees
  end

  def quantity_available_to_sell
    user.shares_available_for_property(property)
  end

  private

  def check_kyc_limitations
    user.check_kyc_limitations!
  end

  def reject_with_reason(reason)
    update_column(:reason, reason)
    reject!
  end
end
