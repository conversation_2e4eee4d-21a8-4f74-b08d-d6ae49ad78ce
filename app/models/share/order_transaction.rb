class Share::OrderTransaction < ActiveRecord::Base
  belongs_to :order
  belongs_to :user

  validates :order, :user, presence: true
  validates :quantity, numericality: true, presence: true

  attr_accessor :skip_receipt

  after_save :send_receipt

  private

  def send_receipt
    return unless order.class == Share::SellOrder
    return if skip_receipt

    ShareSellOrderDelivery.notify(:sold, order, quantity)
  rescue => e
    Rails.logger.error("Unable to send Share::OrderTransaction receipt. #{id} / #{e.message}")
  end
end
