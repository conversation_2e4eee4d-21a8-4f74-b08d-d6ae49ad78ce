class Share::SellOrder < Share::Order
  include SharedOrder

  aasm do
    state :pending, initial: true
    state :active
    state :completed
    state :rejected
    state :cancelled

    event :activate do
      transitions from: :pending, to: :active
    end

    event :complete do
      transitions from: :active, to: :completed
    end

    event :reject do
      transitions to: :rejected
    end

    event :cancel do
      transitions to: :cancelled, guard: :mark_cancelled
    end
  end

  after_create :activate!

  def queue_cancellation!
    CancelOrderJob.perform_later(id: id) if active?
  end
end
