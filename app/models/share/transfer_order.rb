class Share::TransferOrder < Share::Order
  belongs_to :creator, class_name: 'User'
  belongs_to :source_user, class_name: 'User'

  attr_accessor :quantity_confirmation

  aasm do
    state :pending, initial: true
    state :completed
    state :rejected

    event :complete do
      transitions from: :pending, to: :completed, after: :send_receipt
    end

    event :reject do
      transitions to: :rejected
    end
  end

  validates :creator, :description, :source_user, presence: true
  validate :check_quantity_confirmation, on: :create
  validate :check_user_has_enough_shares, on: :create

  after_commit :queue!, on: :create

  def queue!
    ShareOrderJob.perform_later(id: id)
  end

  def send_receipt
    ShareTransferOrderDelivery.notify(:complete, self)
  rescue => e
    Rails.logger.error("Unable to send Share::TransferOrder receipt. #{id} / #{e.message}")
  end

  private

  def check_quantity_confirmation
    errors.add(:quantity_confirmation, 'doesnt match quantity') unless quantity == quantity_confirmation.to_i
  end

  def check_user_has_enough_shares
    return unless quantity && source_user && property

    total_available = source_user.shares_available_for_property(property)

    errors.add(:source_user_id, "only has #{total_available} shares available") if quantity > total_available
  end
end
