class User < ActiveRecord::Base
  TITLES = %w(Mr Mrs Miss Ms Dr Prof Rev Other).freeze
  MANGOPAY_DISALLOWED_COUNTRIES = %w{AF BS BA BW KH KP ET GH GY IR IQ LA UG PK RS LK SY TT TN VU YE}
  DISALLOWED_COUNTRIES = (MANGOPAY_DISALLOWED_COUNTRIES + %w{AS MP PA UM US}).freeze
  DISALLOWED_NATIONALITIES = %w(AS MP UM US).freeze

  MAX_KYC_CREDIT = 120_000 # 1200 GBP
  MIN_AGE = 18

  has_secure_password

  include Auth::Authentication
  include Auth::Lockable
  include Auth::Roles
  include UserStates
  # include UserBank
  include UserCertification
  include UserWallet
  include UserQueuedActions
  include UserReferer

  attr_accessor :identity_proof,
                :registration_proof,
                :articles_of_association,
                :shareholder_declaration

  scope :active_investors, -> { where.not(mangopay_id: Mango::LegalUser::ROOT_USER_ID).joins(:share_logs).group('share_logs.user_id').having('SUM(share_logs.quantity) > 0') }

  belongs_to :certification_level, class_name: 'Certification::Level', optional: true

  has_many :addresses, dependent: :destroy
  has_many :directors, -> { where(kind: 'director') }, class_name: 'User::Address'
  has_many :shareholders, -> { where(kind: 'shareholder') }, class_name: 'User::Address'

  has_one :address, -> { where(kind: 'personal') }, class_name: 'User::Address'
  has_one :headquarters, -> { where(kind: 'headquarters') }, class_name: 'User::Address'

  has_many :certification_attempts, class_name: 'User::CertificationAttempt', dependent: :destroy
  has_many :dividends, dependent: :destroy, class_name: 'Property::Dividend'
  has_many :login_attempts, class_name: 'User::LoginAttempt', dependent: :destroy
  has_many :mandates, dependent: :destroy
  has_many :news_items, class_name: 'Property::NewsItem', dependent: :destroy
  has_many :payment_logs, dependent: :destroy
  has_many :potential_investments, dependent: :destroy
  has_many :queued_actions, class_name: 'User::QueuedAction', dependent: :destroy
  has_many :notifications, as: :recipient, dependent: :destroy, class_name: 'Noticed::Notification'

  has_many :kyc_documents, dependent: :destroy
  has_many :shufti_kyc_documents
  has_many :mangopay_kyc_documents
  has_many :onfido_kyc_documents

  has_many :share_orders, class_name: 'Share::Order'
  has_many :buy_orders, class_name: 'Share::BuyOrder'
  has_many :sell_orders, class_name: 'Share::SellOrder'
  has_many :share_logs, class_name: 'Share::Log'
  has_one :remote_bank_account, as: :bankable, dependent: :destroy

  accepts_nested_attributes_for :address, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :headquarters, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :addresses, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :directors, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :shareholders, allow_destroy: true, reject_if: :all_blank

  # Pending State
  validates :email, uniqueness: { case_sensitive: false }, email: true, presence: true

  # KYC Light
  validates :first_name, :last_name, presence: true, if: :kyc_light_is_complete?

  # Titles
  validates :title, inclusion: { in: TITLES, allow_blank: true }

  # Countries
  validates :nationality, :country_of_residence,
            inclusion: { in: ISO3166::Country.codes, allow_blank: true }

  # Lengths
  validates :last_name, length: { minimum: 2, allow_blank: true }

  before_validation :format_attributes

  # Restricted Countries / Age
  after_save :reject_if_invalid_country!
  after_save :reject_if_invalid_nationality!
  after_save :reject_if_under_age!

  # Admin Notification
  after_create :send_admin_notification

  validate :ensure_phone_not_changed_if_verified

  def full_name
    [first_name, last_name].join(' ')
  end

  def display_name
    full_name.present? ? full_name : email
  end

  def confirm!
    self.confirmation_token = nil
    self.confirmed_at = Time.now
  end

  def amount_debited(start_date = Time.now - 1.year, end_date = Time.now)
    calculate_payment_log_total('debit', start_date, end_date)
  end

  def amount_credited(start_date = Time.now - 1.year, end_date = Time.now)
    calculate_payment_log_total('credit', start_date, end_date)
  end

  def check_kyc_limitations!
    return false if kyc_regular_is_required? ||
                    kyc_regular_is_complete? ||
                    kyc_regular_is_pending? ||
                    kyc_regular_has_failed?

    return false unless over_kyc_limitations?

    kyc_regular_required!
  end

  def over_kyc_limitations?(credit: 0)
    return false if kyc_regular_is_complete?

    return true if (amount_credited + credit) > MAX_KYC_CREDIT

    false
  end

  def shares_available_for_property(property)
    total = Share::Log.where(property: property, user: self).sum(:quantity)

    # Remove any pending share transfer orders
    total -= Share::TransferOrder.pending
                                 .where(property: property, source_user: self)
                                 .sum(:quantity)

    # Remove any pending easy exit orders
    total -= Share::EasyExitOrder.where(aasm_state: %w(pending active), property: property, user: self)
                                 .collect(&:quantity_remaining)
                                 .sum

    # Remove any pending exit orders
    total -= Share::ExitOrder.where(aasm_state: %w(pending active), property: property, user: self)
                             .collect(&:quantity_remaining)
                             .sum

    # We need to be able to transfer shares on unfunded properties
    # as these are all tied up in the initial share order we need
    # to ignore that if the user is the root user
    unless self == User.admin_uown
      total -= Share::SellOrder.active
                               .where(property: property, user: self)
                               .collect(&:quantity_remaining)
                               .sum
    end

    total
  end

  def beneficiary(amount, ete_id)
    return unless remote_bank_account&.valid_bank?

    {
      beneficiary: {
        name: full_name
      },
      beneficiaryAccount: {
        domesticAccountNumber: remote_bank_account&.account_number,
        domesticAccountCountry: "GB",
        domesticBranchCode: remote_bank_account&.sort_code
      },
      paymentAmount: amount.to_d / 100,
      endToEndId: ete_id
    }
  end

  def self.admin_uown
    user = User.find_by(email: '<EMAIL>')

    unless user
      user = User.new(first_name: 'uOWN',
                      last_name: 'Admin',
                      email: '<EMAIL>',
                      aasm_state: 'kyc_regular_is_complete')

      user.save(validate: false)
    end

    user
  end

  private

  def format_attributes
    self.first_name = first_name.titleize_with_respect if first_name
    self.middle_name = middle_name.titleize_with_respect if middle_name
    self.last_name = last_name.titleize_with_respect if last_name
    self.email = email.downcase if email
  end

  # Country
  def invalid_country?
    (country_of_residence.present? && DISALLOWED_COUNTRIES.include?(country_of_residence)) ||
      (address&.country.present? && DISALLOWED_COUNTRIES.include?(address.country))
  end

  def reject_if_invalid_country!
    return if already_rejected? || !invalid_country?

    reject_country!
  end

  # Nationality

  def invalid_nationality?
    nationality.present? && DISALLOWED_NATIONALITIES.include?(nationality)
  end

  def reject_if_invalid_nationality!
    return if already_rejected? || !invalid_nationality?

    reject_nationality!
  end

  # Age

  def under_age?
    date_of_birth.present? && date_of_birth > MIN_AGE.years.ago
  end

  def reject_if_under_age!
    return if already_rejected? || !under_age?

    reject_age!
  end

  def already_rejected?
    rejected_country? || rejected_nationality? || rejected_age?
  end

  def calculate_payment_log_total(direction, start_date, end_date)
    payment_logs.where(failed_at: nil)
                .where(direction: direction)
                .where(created_at: start_date..end_date)
                .sum(:amount)
  end

  def send_admin_notification
    AdminNotification.signup(self)
  end

  def ensure_phone_not_changed_if_verified
    return unless phone_number_changed? && phone_verified

    errors.add(:phone_number, "cannot be changed once verified")
  end
end
