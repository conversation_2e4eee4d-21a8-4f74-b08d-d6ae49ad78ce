class User::Address < ActiveRecord::Base
  DIRECTOR = 'director'.freeze
  HEADQUARTERS = 'headquarters'.freeze
  PERSONAL = 'personal'.freeze
  SHAREHOLDER = 'shareholder'.freeze
  KINDS = [DIRECTOR, HEADQUARTERS, PERSONAL, SHAREHOLDER].freeze

  belongs_to :user, inverse_of: :addresses

  validates :address_1, :city, :post_code, presence: true
  validates :first_name, :last_name, :date_of_birth, presence: true, if: :name_and_dob_required?
  validates :kind, presence: true, inclusion: { in: KINDS }
  validates :country, presence: true, inclusion: { in: ISO3166::Country.codes }

  validate :check_postcode_format

  before_save :format_address
  after_save :sync_user_to_flg_crm

  private

  def check_postcode_format
    return unless post_code.present? && country.present? && country == 'GB'

    errors.add(:post_code, 'is invalid') unless UKPostcode.parse(post_code).full_valid?
  end

  def format_address
    self.address_1 = address_1&.titleize_with_respect
    self.address_2 = address_2&.titleize_with_respect
    self.city = city&.titleize_with_respect
    self.post_code = post_code&.upcase
  end

  def name_and_dob_required?
    [DIRECTOR, SHAREHOLDER].include?(kind)
  end

  def sync_user_to_flg_crm
    return if self.user.registered?
    return unless Rails.env.production?

    SyncFlgToCrmJob.perform_later(self.user.id)
  end
end
