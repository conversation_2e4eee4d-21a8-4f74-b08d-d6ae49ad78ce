class User::MangopayKycDocument < User::KycDocument
  KINDS =  %w[identity_proof registration_proof articles_of_association shareholder_declaration].freeze
  INITIAL_STATE = 'CREATED'.freeze
  PENDING_STATE = 'VALIDATION_ASKED'.freeze
  VALIDATED_STATE = 'VALIDATED'.freeze
  REFUSED_STATE = 'REFUSED'.freeze
  OUT_OF_DATE_STATE = 'OUT_OF_DATE'.freeze

  STATES = [INITIAL_STATE, PENDING_STATE, VALIDATED_STATE, REFUSED_STATE, OUT_OF_DATE_STATE].freeze

  scope :pending,   -> { where(state: PENDING_STATE) }
  scope :validated, -> { where(state: VALIDATED_STATE) }
  scope :refused,   -> { where(state: REFUSED_STATE) }

  validates :kind, inclusion: { in: KINDS }
  validates :state, inclusion: { in: STATES }
  validates :base_64_image, presence: true, on: :create

  def remote_document
    @remote_document ||= Mango::KYC.find_by_id(user: user,
                                               kyc_document_id: kyc_document_id) rescue nil
  end

  def update_status
    return false unless kyc_document_id && remote_document

    self.state = remote_document.status
    save!
  end

  def failure_reason
    remote_document&.refused_reason_type
  end

  def pending?
    state == PENDING_STATE
  end

  def validated?
    state == VALIDATED_STATE
  end

  def refused?
    state == REFUSED_STATE
  end

  private

  def create_remote
    return unless base_64_image

    remote_document = Mango::KYC.create(user: user, kind: kind, image: base_64_image)
    self.kyc_document_id = remote_document.id
  end

  def set_initial_state
    self.state = PENDING_STATE if state.blank?
  end
end
