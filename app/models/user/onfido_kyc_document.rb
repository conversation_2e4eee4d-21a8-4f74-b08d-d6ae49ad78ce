# @deprecated Onfido connection has now been removed. This model has been left for the purpose
# of viewing historical data only.

class User::OnfidoKycDocument < User::KycDocument
  KINDS = %w[document identity watchlist].freeze
  PENDING_STATE = 'in_progress'.freeze
  VALIDATED_STATE = 'clear'.freeze
  REFUSED_STATES = %w[consider unidentified].freeze
  STATES = ([PENDING_STATE, VALIDATED_STATE] + REFUSED_STATES).freeze

  scope :pending,   -> { where(state: PENDING_STATE) }
  scope :validated, -> { where(state: VALIDATED_STATE) }
  scope :refused,   -> { where(state: REFUSED_STATES) }

  validates :kind, inclusion: { in: KINDS }
  validates :state, inclusion: { in: STATES }

  def remote_document
    nil
  end

  def update_status
    self
  end

  def failure_reason
    nil
  end

  def pending?
    state == PENDING_STATE
  end

  def validated?
    state == VALIDATED_STATE
  end

  def refused?
    REFUSED_STATES.include?(state)
  end

  private

  def create_remote
    true
  end

  def set_initial_state
    self.state = PENDING_STATE if state.blank?
  end
end
