class User::QueuedAction < ActiveRecord::Base
  include AASM

  aasm do
    state :pending, initial: true
    state :completed
    state :failed
    state :cancelled

    event :complete do
      transitions from: :pending, to: :completed
    end

    event :fail do
      transitions from: :pending, to: :failed
    end

    event :cancel do
      transitions from: :pending, to: :cancelled
    end
  end

  belongs_to :user
  belongs_to :source, polymorphic: true, required: false
  belongs_to :target, polymorphic: true, required: false

  validates :user, presence: true

  def process!
    raise 'Must be implemented by subclass'
  end

  def fail_with_message(message)
    update_column(:message, message)
    fail!
  end
end
