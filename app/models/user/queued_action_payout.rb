class User::QueuedActionPayout < User::QueuedAction
  validates :amount, :bank_account_id, presence: true

  def process!
    Mango::Payout.create(user: user,
                         bank_account_id: bank_account_id,
                         amount: amount)

    complete!
  rescue => e
    fail_with_message(e.message)
  end

  def self.first_payout_bank?(user:, bank_account_id:)
    User::QueuedActionPayout.completed
                            .where(user_id: user.id, bank_account_id: bank_account_id)
                            .none?
  end
end
