class User::ShuftiKycDocument < User::KycDocument
  DEFAULT_STATE = 'request.pending'.freeze
  VALIDATED_STATE = 'verification.accepted'.freeze
  PENDING_STATES = ['request.received', 'review.pending'].freeze
  REFUSED_STATES = ['verification.cancelled', 'request.timeout', 'verification.declined', 'request.deleted'].freeze

  scope :pending, -> { where(state: PENDING_STATES) }
  scope :validated, -> { where(state: 'verification.accepted') }
  scope :refused, -> { where(state: REFUSED_STATES) }
  scope :initialized, -> { where(state: DEFAULT_STATE) }

  def update_status
    res = ::Shufti::StatusService.new(reference: kyc_document_id).call
    if res.success?
      event = res.body['event']
      self.state = res.body['event']
      save!

      aasm = if event == 'verification.accepted'
               'kyc_regular_is_complete'
             elsif PENDING_STATES.include?(event)
               'kyc_regular_is_pending'
             elsif REFUSED_STATES.include?(event)
               'kyc_regular_has_failed'
             end
      user.update(aasm_state: aasm)
    end
  end

  def failure_reason
  end

  def pending?
    PENDING_STATES.include?(state)
  end

  def validated?
    state == VALIDATED_STATE
  end

  def refused?
    REFUSED_STATES.include?(state)
  end

  def self.pending?(event)
    PENDING_STATES.include?(event)
  end

  def self.validated?(event)
    event == VALIDATED_STATE
  end

  def self.refused?(event)
    REFUSED_STATES.include?(event)
  end

  private

  def create_remote
  end

end
