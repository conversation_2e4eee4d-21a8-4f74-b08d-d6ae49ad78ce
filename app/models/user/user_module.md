# Module: user

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
* `{Method1}`: {description}
* `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
* [Task1]: {brief description}
* [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
* [Task1]: {brief description}
* [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---

---KEY_DEFINITIONS_START---
Key Definitions:
2Gf: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user
2Gf1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/address.rb
2Gf2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/certification_attempt.rb
2Gf3: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/kyc_document.rb
2Gf4: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/legal.rb
2Gf5: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/login_attempt.rb
2Gf6: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/mangopay_kyc_document.rb
2Gf7: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/natural.rb
2Gf8: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/onfido_kyc_document.rb
2Gf9: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/queued_action.rb
2Gf10: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/queued_action_investment.rb
2Gf11: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/queued_action_payout.rb
2Gf12: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/shufti_kyc_document.rb
2Gf13: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/models/user/state.rb
---KEY_DEFINITIONS_END---

last_KEY_edit: Assigned keys: 2Gf, 2Gf1, 2Gf2, 2Gf3, 2Gf4, 2Gf5, 2Gf6, 2Gf7, 2Gf8, 2Gf9, 2Gf10, 2Gf11, 2Gf12, 2Gf13
last_GRID_edit: Manual dep: queued_action_payout.rb -> [2Gf(user), 2Gf1(address.rb), 2Gf10(queued_action_investment.rb), 2Gf12(shufti_kyc_document.rb), 2Gf13(state.rb), 2Gf2(certification_attempt.rb), 2Gf3(kyc_document.rb), 2Gf4(legal.rb), 2Gf5(login_attempt.rb), 2Gf6(mangopay_kyc_document.rb), 2Gf7(natural.rb), 2Gf8(onfido_kyc_document.rb)] (n) (2025-06-10T13:13:42.620306)

---GRID_START---
X 2Gf 2Gf1 2Gf2 2Gf3 2Gf4 2Gf5 2Gf6 2Gf7 2Gf8 2Gf9 2Gf10 2Gf11 2Gf12 2Gf13
2Gf = ox13
2Gf1 = non12
2Gf2 = nnon11
2Gf3 = n3on10
2Gf4 = n4onnxn6
2Gf5 = n5on8
2Gf6 = n3<nnon7
2Gf7 = n4xnnon6
2Gf8 = n3<n4on5
2Gf9 = n9oxxnn
2Gf10 = n9xon3
2Gf11 = n9xnonn
2Gf12 = n3<n8on
2Gf13 = n13o
---GRID_END---

---mini_tracker_end---
