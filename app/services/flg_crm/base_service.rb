require 'nokogiri'
require 'rest-client'
require 'ostruct'

class FlgCrm::BaseService
  def initialize(action:, payload:)
    @base_url = Rails.application.secrets.flg_crm[:base_url]
    @api_key = Rails.application.secrets.flg_crm[:api_key]
    @lead_group_id = Rails.application.secrets.flg_crm[:lead_group_id]
    @site_id = Rails.application.secrets.flg_crm[:site_id]
    @action = action
    @payload = payload
  end

  def call
    endpoint = end_points(action)
    @client = RestClient::Resource.new(base_url, headers: { 'Content-Type': 'application/xml' })
    @client[endpoint].post(payload)
  rescue RestClient::ExceptionWithResponse => e
    e.response
  end

  private

  attr_reader :client, :base_url, :action, :payload, :api_key, :lead_group_id, :site_id

  def end_points(action)
    case action
    when 'read', 'search', 'destroy'
      '/APILead.php'
    when 'create'
      '/APILeadCreateUpdate.php'
    end

  end
end