require 'nokogiri'
require 'rest-client'
require 'ostruct'

class FlgCrm::Client
  BASE_URL = Rails.application.secrets.flg_crm[:base_url]
  UPSERT_ENDPOINT = Rails.application.secrets.flg_crm[:upsert_endpoint]


  def initialize
    @client = RestClient::Resource.new(BASE_URL, headers: { 'Content-Type': 'application/xml' })
  end

  def upsert_user(user_payload)
    begin
      response = @client[UPSERT_ENDPOINT].post(user_payload)
      parse_response(response)
    rescue RestClient::ExceptionWithResponse => e
      parse_response(e.response)
    end
  end

  private

  def parse_response(response)
    doc = Nokogiri::XML(response.body)
    if doc.xpath('//status').text == '0'
      OpenStruct.new(success?: true, id: doc.xpath('//id').text, data: response.body)
    else
      OpenStruct.new(success?: false, error_message: doc.xpath('//message').text)
    end
  end
end
