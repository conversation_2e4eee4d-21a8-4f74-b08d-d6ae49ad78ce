require 'nokogiri'
require 'rest-client'
require 'ostruct'

class FlgCrm::DestroyUserService < FlgCrm::BaseService
  def initialize(flg_lead_id:)
    @id = flg_lead_id
    super(action: 'destroy', payload: payload)
  end

  def call
    res = parse_response(super)
    res.xpath('//code').text
  end

  private
  attr_reader :id

  def payload
    builder = Nokogiri::XML::Builder.new do |xml|
      xml.data {
        xml.key @api_key
        xml.request 'delete'
        xml.id id
      }
    end
    builder.to_xml
  end

  def parse_response(response)
    Nokogiri::XML(response.body)
  end
end
