# Module: flg_crm

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
* `{Method1}`: {description}
* `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
* [Task1]: {brief description}
* [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
* [Task1]: {brief description}
* [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---

---KEY_DEFINITIONS_START---
Key Definitions:
2Ia: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/services/flg_crm
2Ia1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/services/flg_crm/base_service.rb
2Ia2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/services/flg_crm/client.rb
2Ia3: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/services/flg_crm/destroy_user_service.rb
2Ia4: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/services/flg_crm/search_user_service.rb
2Ia5: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/services/flg_crm/users.rb
---KEY_DEFINITIONS_END---

last_KEY_edit: Assigned keys: 2Ia, 2Ia1, 2Ia2, 2Ia3, 2Ia4, 2Ia5
last_GRID_edit: Manual dep: destroy_user_service.rb -> [2Ia2(client.rb), 2Ia4(search_user_service.rb), 2Ia5(users.rb)] (n) (2025-06-12T10:42:44.965348)

---GRID_START---
X 2Ia 2Ia1 2Ia2 2Ia3 2Ia4 2Ia5
2Ia = ox5
2Ia1 = xon>>n
2Ia2 = xnon3
2Ia3 = x<nonn
2Ia4 = x<ppop
2Ia5 = xp4o
---GRID_END---

---mini_tracker_end---
