require 'nokogiri'
require 'rest-client'
require 'ostruct'

class FlgCrm::SearchUserService < FlgCrm::BaseService
  def initialize
    super(action: 'search', payload: payload)
  end

  def call
    parse_response(super)
  end

  private

  def payload
    builder = Nokogiri::XML::Builder.new do |xml|
      xml.data {
        xml.key @api_key
        xml.leadgroup @lead_group_id
        xml.site @site_id
        xml.request 'search'
        xml.startdate 1.month.ago.strftime('%d/%m/%Y')
        xml.enddate Date.today.strftime('%d/%m/%Y')
      }
    end
    builder.to_xml
  end

  def parse_response(response)
    Nokogiri::XML(response.body)
  end
end
