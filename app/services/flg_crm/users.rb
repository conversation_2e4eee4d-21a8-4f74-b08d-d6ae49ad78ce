require 'nokogiri'
require 'ostruct'

class FlgCrm::Users
  API_KEY = Rails.application.secrets.flg_crm[:api_key]
  LEAD_GROUP_ID = Rails.application.secrets.flg_crm[:lead_group_id]
  SITE_ID = Rails.application.secrets.flg_crm[:site_id]

  def initialize(user)
    @user = user
    @crm_client = FlgCrm::Client.new
  end

  def sync_to_crm
    @response = crm_client.upsert_user(user_payload)
    return update_flg_id if response.success?

    handle_error(response)
    rescue => e
      OpenStruct.new(success?: false, errors: "error message = #{e.message} and backtrace = #{e.backtrace}")
  end

  private

  attr_accessor :user, :response, :crm_client

  def user_payload
    builder = Nokogiri::XML::Builder.new do |xml|
      xml.data {
        xml.lead {
          xml.key API_KEY
          xml.leadgroup LEAD_GROUP_ID
          xml.site SITE_ID
          xml.id user.flg_lead_id.to_i unless user.flg_lead_id.blank?
          xml.type
          xml.status 'New'
          xml.reference
          xml.source
          xml.medium
          xml.term
          xml.cost 0.0
          xml.value current_investments_in_property
          xml.title user.title
          xml.firstname user.first_name
          xml.lastname user.last_name
          xml.phone1 user.phone_number
          xml.phone2
          xml.email user.email
          xml.address user.address ? user.address.address_1 : ''
          xml.address2 user.address ? user.address.address_2 : ''
          xml.address3
          xml.towncity user.address ? user.address.city : ''
          xml.postcode user.address ? user.address.post_code : ''
          xml.dob user.date_of_birth ? user.date_of_birth.strftime('%d/%m/%Y') : ''
          xml.contactphone 'Yes'
          xml.contactsms 'Yes'
          xml.contactemail 'Yes'
          xml.contactmail 'Yes'
          xml.contactfax 'Yes'
          xml.data1 user.aasm_state
          xml.data2
          xml.data3
          xml.data4
        }
      }
    end
    builder.to_xml
  end

  def update_flg_id
    user.flg_lead_id = response.id
    resp = if user.save(validate: false)
      { success?: true, errors: '' }
    else
      { success?: false, errors: user.errors&.full_messages&.to_sentence}
    end
    OpenStruct.new(resp)
  end

  def handle_error(response)
    Rails.logger.error("Failed to sync user: #{response.error_message}")
    OpenStruct.new(success?: false, errors: response.error_message)
  end

  def current_investments_in_property
    property_ids = Property.where.not(type: 'Property::Cause').ids
    user_share_logs = ::Share::Log.select('sum(share_logs.total_amount) as total,
                         max(share_logs.id),
                         share_logs.property_id,
                         share_logs.user_id')
                                  .includes(:property)
                                  .where(user_id: user.id)
                                  .where(property_id: property_ids)
                                  .group(:property_id)
                                  .sort { |a, b| b.total <=> a.total }
    (user_share_logs.sum { |share_log| share_log.total }) / 100
  end
end
