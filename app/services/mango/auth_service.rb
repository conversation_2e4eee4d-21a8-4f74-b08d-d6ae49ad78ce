require 'rest-client'

class Mango::AuthService
  def initialize
    @headers = {
      "Authorization" => "Basic #{Base64.strict_encode64(access_token)}"
    }
  end

  private

  attr_reader :headers

  def mango_access_token
    resp = RestClient::Resource.new(MangoPay.api_uri.to_s, headers: headers)['/v2.01/oauth/token'].post({ grant_type: 'client_credentials' })
    JSON.parse(resp.body)['access_token']
  end

  def access_token
    "#{Rails.application.secrets.mangopay_client_id}:#{Rails.application.secrets.mangopay_client_passphrase}"
  end
end