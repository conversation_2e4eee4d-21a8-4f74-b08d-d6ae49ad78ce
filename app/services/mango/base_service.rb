require 'rest-client'
require 'ostruct'

class Mango::BaseService < Mango::AuthService
  def initialize(sub_url, payload = {}, req_method = 'get')
    super()
    @endpoint = "#{MangoPay.api_uri.to_s}#{MangoPay.api_path}"
    @payload = payload
    @req_method = req_method
    @sub_url = sub_url
    @headers = { Authorization: "Bearer #{mango_access_token}", Content_Type: 'application/json' }
  end

  def call
    res = RestClient::Resource.new(endpoint, headers: headers)[sub_url].send(req_method, payload.to_json)
    OpenStruct.new(success?: true, data: res)
  rescue RestClient::ExceptionWithResponse => e
    OpenStruct.new(success?: false, error: e.http_body)
  end

  private

  attr_reader :headers, :endpoint, :payload, :req_method, :sub_url

end
