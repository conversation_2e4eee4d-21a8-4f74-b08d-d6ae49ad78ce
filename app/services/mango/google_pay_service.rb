require 'rest-client'

class Mango::GooglePayService < Mango::BaseService
  def initialize(mango_id, wallet_id, amount, payment_data, browser_infos, p_inv_id,  fee=0, currency='GBP')
    @mango_id = mango_id
    @wallet_id = wallet_id
    @amount = amount
    @fee = fee
    @currency = currency
    @payment_data = payment_data
    @browser_infos = browser_infos
    @p_inv_id = p_inv_id
    super('payins/payment-methods/googlepay', payload, 'post')
  end

  private

  attr_reader :mango_id, :wallet_id, :amount, :fee, :currency, :payment_data, :browser_infos, :p_inv_id

  def payload
    {
      "AuthorId": mango_id,
      "CreditedWalletId": wallet_id,
      "DebitedFunds": {
        "Currency": currency,
        "Amount": amount
      },
      "Fees": {
        "Currency": currency,
        "Amount": fee
      },
      "Tag": "Google Pay PayIn",
      "IpAddress": browser_infos[:IpAddress],
      "SecureModeReturnURL": "#{Rails.application.secrets.frontend_url}/payins/google_pays/handle_response?p_inv_id=#{p_inv_id}",
      "SecureMode": "FORCE",
      "ReturnURL": "#{Rails.application.secrets.frontend_url}/payins/google_pays/handle_response?p_inv_id=#{p_inv_id}",
      "BrowserInfo": browser_infos.to_h,
      "PaymentData": payment_data
    }
  end
end


