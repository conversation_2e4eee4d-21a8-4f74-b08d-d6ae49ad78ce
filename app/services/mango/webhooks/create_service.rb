class Mango::Webhooks::CreateService
  def initialize(event_type)
    @event_type = event_type
  end
  def call
    begin
      MangoPay::Hook.create(hook_object)
    rescue MangoPay::ResponseError => error
      puts "Failed to create Hook: #{error.message} and Error details: #{error.details}"
    end
  end


  private

  attr_reader :event_type
  def hook_object
    {
      Url: "#{Rails.application.secrets.frontend_url}/users/kycs/webhook",
      EventType: event_type
    }
  end
end


