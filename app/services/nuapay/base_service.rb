require 'rest-client'
require 'base64'
require 'json'

class Nuapay::BaseService
  class RequestError < StandardError; end
  Result = Struct.new(:success?, :status, :body, :error,)
  def initialize(endpoint:, action: 'get', payload: {}, query: {})
    @action = action
    @endpoint = endpoint
    @payload = payload.to_json
    @query = query
    @client = RestClient::Resource.new(Rails.application.secrets.nuapay[:sandbox_url])

  end

  def call
    response = client[endpoint].public_send *args
    Result.new(success?: true, status: response.code, body: json_parse(response.body))

  rescue RestClient::ExceptionWithResponse => e
    Result.new(success?: false, status: e.response.code, error: friendly_error_message(e.response.body))
  end

  private

  attr_reader :action, :payload, :endpoint, :query, :client

  def headers
    encoded_api_key = Base64.strict_encode64("#{Rails.application.secrets.nuapay_api_key}")
    hdrs = {
      content_type: :json,
      accept: :json,
      authorization: "Basic #{encoded_api_key}",
      params: query
    }
    hdrs['jws-signature'] =  detached_jws if signature_required?
    hdrs
  end

  def detached_jws
    Nuapay::JwsSignatureService.new(params: payload).call if signature_required?
  end

  def json_parse(data)
    return if data.blank?

    JSON.parse(data)
  end

  def args
    [action].then { |arg| action == 'post' ? arg << payload : arg }
            .then { |arg|  arg << headers  }
  end

  def signature_required?
    action == 'post' &&
      (['/v2/beneficiaries'].include?(endpoint) ||
        endpoint.include?('credittransfers'))
  end

  def friendly_error_message(response)
    response = json_parse(response)
    message = response["returnDescription"]

    details = response["details"]&.map do |error|
      "#{error['field']} #{error['description']}"
    end&.to_sentence

    "#{message} #{details}.".gsub('Please check details.','')
  end
end