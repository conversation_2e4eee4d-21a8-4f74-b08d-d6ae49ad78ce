class Nuapay::CreditTransfers::Batch::ProcessDividendsService < Nuapay::BaseService
  Result = Struct.new(:success?, :status, :body, :error,)
  def initialize(dividend_ids:)
    @dividend_ids = dividend_ids
    @logs = []
  end

  def call
    res = ::Nuapay::CreditTransfers::Batch::CreateService.new(params: batch_credit_transfer_payload).call
    raise Nuapay::BaseService::RequestError, res.error unless res.success?

    payment_logs = @logs.map { |log| log.merge!({ remote_id: res.body['data']['id'] }) }
    PaymentLog.upsert_all(payment_logs)
    mark_ineligible_dividends_defered!
    process_uown_dividend!
    Result.new(success?: true, status: 200, body: res.body['data'])
  rescue Nuapay::BaseService::RequestError => e
    pending_dividends.update_all(aasm_state: 'failed', reason: e.message)
    Result.new(success?: false, status: 500, error: e.message)
  end

  private

  attr_reader :dividend_ids, :logs

  def mark_ineligible_dividends_defered!
    ineligible_dividends = pending_dividends - eligible_dividends - admin_dividend
    ineligible_dividends.each { |dividend| dividend.mark_as_deferred! }
  end

  def pending_dividends
    @dividends ||= Property::Dividend.where(id: dividend_ids).pending.includes(:user, :remote_bank_account)
  end

  def admin_dividend
    @admin_dividends ||= pending_dividends.select { |dividend| dividend.user.email == '<EMAIL>' }
  end

  def eligible_dividends
    @eli_dividends ||= pending_dividends.select do |dividend|
      user = dividend.user
      user.kyc_regular_is_complete? && user.remote_bank_account&.valid_bank?
    end
    @eli_dividends - admin_dividend
  end

  def batch_credit_transfer_payload
    {
      numberOfTransactions: credit_transfer_info.count,
      totalAmount: total_max_amount,
      currency: "GBP",
      type: "EXPRESS",
      batchBooking: true,
      originatorAccount: {
        iban: '**********************'#originator_account
      },
      creditTransferTransactionInformation: credit_transfer_info
    }
  end

  def credit_transfer_info
    @ct_info ||= eligible_dividends.map do |dividend|
      amt = dividend.amount_to_pay
      next if amt.negative? || amt.zero?
      @logs = @logs << { amount: amt, direction: 'credit', dividend_id: dividend.id, kind: 'DIVIDEND', user_id: dividend.user_id }
      dividend.user.beneficiary(amt, dividend.id)
    end
  end

  def originator_account
    account = eligible_dividends.first&.property_bank_account
    raise Nuapay::BaseService::RequestError, 'Originator Account is not valid' if account.invalid_bank?

    account.iban
  end

  def total_max_amount
    credit_transfer_info.sum(0) { |ct| ct[:paymentAmount].to_d }
  end

  def process_uown_dividend!
    dividend = admin_dividend.first
    dividend.complete!
  end
end
