class Nuapay::JwsSignatureService
  def initialize(params: {})
    @params = params.is_a?(Hash) ? params.to_json : params
  end

  def call
    "#{header_encoded}..#{signature_encoded}"
  end

  private

  attr_reader :params

  def private_key
    pk = Base64.urlsafe_decode64(Rails.application.secrets.nuapay[:private_key])
    OpenSSL::PKey::RSA.new(pk)
  end

  def signature_encoded
    signature = private_key.sign(OpenSSL::Digest::SHA256.new, "#{header_encoded}.#{params}")
    Base64.urlsafe_encode64(signature, padding: false)
  end

  def header_encoded
    header_map = {
      alg: "RS256",
      kid: Rails.application.secrets.nuapay[:kid],
      iat: 0,
      iss: "C=GB, L=London, OU=Nuapay API, O=Nuapay, CN=#{Rails.application.secrets.nuapay[:cn]}",
      b64: false,
      crit: ["iat", "iss", "b64"]
    }

    header_json = JSON.pretty_generate(header_map)
    Base64.urlsafe_encode64(header_json, padding: false)
  end
end
