require 'rest-client'
require 'base64'
require 'json'

class Shufti::BaseService
  Result = Struct.new(:success?, :status, :body, :error,)
  def initialize(endpoint:, action: 'get', payload: {}, query: {})
    @action = action
    @endpoint = endpoint
    @payload = payload
    @query = query
    @client = RestClient::Resource.new(Rails.application.secrets.shufti[:base_url])

  end

  def call
    response = client[endpoint].public_send *args
    Result.new(success?: true, status: response.code, body: json_parse(response.body))

  rescue RestClient::ExceptionWithResponse => e
    Result.new(success?: false, status: e.response.code, error: json_parse(e.response.body))
  end

  private

  attr_reader :action, :payload, :endpoint, :query, :client

  def headers
    {
      content_type: :json,
      accept: :json,
      authorization: "Basic #{auth_token}",
      params: query
    }
  end

  def json_parse(data)
    return if data.blank?

    JSON.parse(data)
  end

  def args
    [action].then { |arg| action == 'post' ? arg << payload.to_json : arg }
            .then { |arg|  arg << headers  }
  end

  def auth_token
    Base64.strict_encode64("#{Rails.application.secrets.shufti[:client_id]}:#{Rails.application.secrets.shufti[:secret_key]}")
  end

end