require 'uuid7'
class Shufti::VerificationService < Shufti::BaseService
  def initialize(user:)
    @user = user
    super(action: 'post', endpoint: '/', payload: request_body)
  end

  private
  attr_reader :user

  def generate_reference
    UUID7.generate
  end

  def request_body
    {
      reference: generate_reference,
      callback_url: "#{Rails.application.secrets.frontend_url}/users/kycs/shufti_verification_callback",
      verification_mode: 'any',
      email: user.email,
      face: {},
      document: {
        supported_types: ['id_card', 'driving_license', 'passport'],
        dob: user.date_of_birth.to_s
      }
    }
  end
end
