class EmailValidator < ActiveModel::EachValidator
  # Regex provided by mangopay
  EMAIL_REGEX = /([a-zA-Z0-9!#$%&'*+\/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+\/=?^_`{|}~-]+)*)@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?/i

  def validate_each(record, attribute, value)
    return unless value.present? && (value.strip != value || value !~ EMAIL_REGEX)

    record.errors.add(attribute, :invalid)
  end
end
