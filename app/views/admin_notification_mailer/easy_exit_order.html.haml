= email_paragraph('Admin Notification')

= email_paragraph('An easy exit order has been created. Details are below:')

%table{ align: 'center', bgcolor: '#F6F6F0', border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
  %tr
    %td{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '30' }  
    %td
      %table.spacer{ bgcolor: '#F6F6F0', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%', width: '100%' }
        %tr
          %td.spacer{ height: '20', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }  

      = list_item('User', @easy_exit_order.user.email)
      = list_item('Property', @easy_exit_order.property.name)
      = list_item('Quantity', @easy_exit_order.quantity)
      = list_item('Created', @easy_exit_order.created_at)

    %td{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '30' }  

%table.spacer{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%', width: '100%' }
  %tr
    %td.spacer{ height: '20', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }
