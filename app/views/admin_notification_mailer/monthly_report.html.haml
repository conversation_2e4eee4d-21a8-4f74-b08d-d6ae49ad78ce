= email_paragraph('Admin Notification')

= email_paragraph('Monthly report is generated successfully. Details are below:')

%table{ align: 'center', bgcolor: '#F6F6F0', border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
  %tr
    %td{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '30' }  
    %td
      %table.spacer{ bgcolor: '#F6F6F0', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%', width: '100%' }
        %tr
          %td.spacer{ height: '20', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }  
          
      = list_item('Assests under managment:', number_to_currency(@kpis[:aum], precision: 2, unit: '&pound;'.html_safe))
      = list_item('Monthly invested amount:', number_to_currency(@kpis[:monthly_invested_amount], precision: 2, unit: '&pound;'.html_safe))
      = list_item('User sign-ups:', @kpis[:sign_ups_count])
      = list_item('New investors:', @kpis[:new_investors_count])
      = list_item('Conversion % to investors:', @kpis[:conversion_percent])
      = list_item('Average days to invest for new investors:', @kpis[:avg_days_to_invest])

    %td{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '30' }  

%table.spacer{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%', width: '100%' }
  %tr
    %td.spacer{ height: '20', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }
