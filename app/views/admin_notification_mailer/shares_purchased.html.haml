= email_paragraph('Admin Notification')

= email_paragraph('Shares have been purchased. Details are below:')

%table{ align: 'center', bgcolor: '#F6F6F0', border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
  %tr
    %td{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '30' }  
    %td
      %table.spacer{ bgcolor: '#F6F6F0', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%', width: '100%' }
        %tr
          %td.spacer{ height: '20', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }  

      = list_item('User', @buy_order.user.email)
      = list_item('Property', @buy_order.property.name)
      = list_item('Quantity', @buy_order.quantity)
      = list_item('Amount', currency(@buy_order.total_amount))

    %td{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '30' }  

%table.spacer{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%', width: '100%' }
  %tr
    %td.spacer{ height: '20', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }  
