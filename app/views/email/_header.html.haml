%table{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
  %tr
    %td{ align: 'center', bgcolor: '#ffffff' }
      %table{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
        %tr
          %td.header-ellipse-title{ background: email_asset('<EMAIL>'), height: '302', style: 'background-size: contain; background-repeat: no-repeat;', valign: 'top', width: '432' }
            %div
              %table{ border: '0', cellpadding: '0', cellspacing: '0', style: 'table-layout: auto;', width: '100%' }
                %tbody
                  %tr
                    %td.content-gutter{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '60' }  
                    %td
                      %table{ border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%; table-layout: auto;', width: '100%' }
                        %tbody
                          %tr
                            %td.spacer-h-100{ height: '150', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }  
                      .title-text{ style: 'font-family: Arial, Helvetica, sans-serif; font-size: 42px; line-height: 55px; color: rgb(29, 29, 27); font-weight: bold; text-align: left; margin-bottom: 0px;' }
                        = yield :title

          %td{ valign: 'top', width: '168' }
            / LOGO
            %table{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', style: 'background-color: rgb(255, 255, 255); table-layout: auto;', width: '100%' }
              %tr
                %td
                  %table{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size: 0pt; line-height: 0pt; text-align: center; width: 100%; min-width: 100%; background-color: rgb(255, 255, 255);table-layout: auto;', width: '100%' }
                    %tr
                      %td.spacer-h-25{ height: '35', style: 'font-size: 0pt; line-height: 0pt; text-align: center; width: 100%; min-width: 100%' }  

                  %table{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', style: 'background-color: rgb(255, 255, 255); table-layout: auto;', width: '100%' }
                    %tr
                      %td{ style: 'font-size:0pt; line-height:0pt; text-align:right' }
                        %a{ href: 'https://www.uown.co/', target: '_blank', title: 'UOWN' }
                          = email_image_tag('<EMAIL>', class: 'header-logo', alt: 'UOWN', border: '0', style: 'width: 100%; max-width: 133px; text-align: right;', title: 'UOWN', width: '133' )

                      %td.header-gutter{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '35' }

      %table.spacer{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%', width: '100%' }
        %tr
          %td{ height: '30', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }
