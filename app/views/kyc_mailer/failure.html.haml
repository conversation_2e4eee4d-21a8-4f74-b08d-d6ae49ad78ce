= content_for :title do
  Identity Check: Action Required

= email_paragraph("Hi #{@user.first_name},")

= email_paragraph(t("kyc_mailer.failure.reason.#{@failure_reason}"))

- if ['document_unreadable', 'document_not_accepted', 'document_has_expired', 'document_incomplete', 'document_missing'].include?(@failure_reason)
  = render 'passport'

  = email_button('RE-CERTIFY', @link)

= email_paragraph('Keeping you informed,')

= email_paragraph('Your UOWN team.', last: true)
