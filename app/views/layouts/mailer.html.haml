!!!
%html
  %head
    %meta{ content: 'text/html; charset=utf-8', 'http-equiv' => 'Content-type' }
    %meta{ content: 'width=device-width, initial-scale=1, maximum-scale=1', name: 'viewport' }
    %meta{ content: 'IE=edge', 'http-equiv' => 'X-UA-Compatible' }
    %meta{ content: 'date=no', name: 'format-detection' }
    %meta{ content: 'address=no', name: 'format-detection' }
    %meta{ content: 'telephone=no', name: 'format-detection' }
    %title
    %style{ media: 'screen', type: 'text/css' }
      :cdata
        \/* EMAIL CLIENT FIXES */
        body { padding:0 !important; margin:0 !important; display:block !important; min-width:100% !important; width:100% !important; background:#F5F3EB; -webkit-text-size-adjust:none }
        a { color:#646363; text-decoration:none }
        p { padding:0 !important; margin:0 !important }
        img { outline: none; text-decoration: none; border: none; -ms-interpolation-mode: bicubic; /* ALLOW SMOOTHER RENDERING OF RESIZED IMAGE IN INTERNET EXPLORER */ }
        \#outlook a { padding:0; } /* --- FORCE OUTLOOK TO PROVIDE A 'VIEW IN BROWSER' MENU LINK. */
        \.ReadMsgBody { width:100%; } .ExternalClass { width:100%; } /* --- FORCE HOTMAIL TO DISPLAY EMAILS AT FULL WIDTH */
        \.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div { line-height: 100%; }
        table td { border-collapse: collapse;}
        table { border-collapse:collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; } /* --- FORCE HOTMAIL TO DISPLAY NORMAL LINE SPACING. */
        a[x-apple-data-detectors] { color: inherit !important; text-decoration: none !important; font-size: inherit !important; font-family: inherit !important; font-weight: inherit !important; line-height: inherit !important; }
        \/* END EMAIL CLIENT FIXES */

        \/* MEDIA QUERY SCREEN 480px */
        @media only screen and (max-device-width: 480px), only screen and (max-width: 480px) {
        div[class='mobile-br-1'] { height: 1px !important; }
        div[class='mobile-br-5'] { height: 5px !important; }
        div[class='mobile-br-10'] { height: 10px !important; }
        div[class='mobile-br-15'] { height: 15px !important; }
        div[class='mobile-br-25'] { height: 25px !important; }

        \/* SPACING STYLES ON MOBILE */
        td[class='spacer-h-0'] { height: 0px !important; }
        td[class='spacer-h-5'] { height: 10px !important; }
        td[class='spacer-h-10'] { height: 10px !important; }
        td[class='spacer-h-20'] { height: 20px !important; }
        td[class='spacer-h-25'] { height: 25px !important; }
        td[class='spacer-h-30'] { height: 30px !important; }
        td[class='spacer-h-40'] { height: 40px !important; }
        td[class='spacer-h-100'] { height: 100px !important; }

        \/* FONT STYLES ON MOBILE */
        div[class='title-text'] { font-size: 25px !important; line-height: 30px !important; }
        div[class='h2-title'] { font-size: 24px !important; line-height: 28px !important; }
        div[class='body-text'] { font-size: 16px !important; line-height: 24px !important; }
        div[class='bullet-text'] { font-size: 14px !important; line-height: 20px !important; }
        div[class='footer-text'] { font-size: 8px !important; line-height: 10px !important; }
        div[class='text-left'] { text-align: left !important; }
        th[class='m-td'],
        td[class='m-td'],
        table[class='hide-for-mobile'],
        div[class='hide-for-mobile'],
        span[class='hide-for-mobile'] { display: none !important; width: 0 !important; height: 0 !important; font-size: 0 !important; line-height: 0 !important; min-height: 0 !important; }
        span[class='mobile-block'] { display: block !important; }
        div[class='wgmail'] img { min-width: 320px !important; width: 320px !important; }
        td[class='center'] { text-align: center !important; }
        div[class='fluid-img'] img,
        td[class='fluid-img'] img { width: 100% !important; max-width: 480px !important; height: auto !important; }
        table[class='mobile-shell'] { width: 100% !important; min-width: 100% !important; }
        table[class='center'] { margin: 0 auto; }
        td[class='column'],
        th[class='column'] { float: left !important; width: 100% !important; display: block !important; }
        td[class='td'] { width: 100% !important; min-width: 100% !important; }
        td[class='header-gutter'] { width: 30px !important; }
        td[class='content-gutter'] { width: 30px !important; }
        a[href^="tel"], a[href^="sms"] { text-decoration: none; color: black; pointer-events: none; cursor: default; }
        \.mobile_link a[href^="tel"], .mobile_link a[href^="sms"] { text-decoration: default; color: orange !important; pointer-events: auto; cursor: default; }
        td[class='header-ellipse-title'] { background-size: contain !important; height: 200px !important; }
        td[class='header-hero'] { background-size: contain !important; height: 323px !important; }
        table[class='referral-buttons'] { width: 340px !important; }
        img[class='referral-button'] { width: 100px !important; max-width: 100px !important; }
        table[class='trustpilot-ratings'] { width: 360px !important; }
        img[class='trustpilot-rating'] { width: 72px !important; max-width: 72px !important; }
        table[class='footer-ellipse'] { width: 158px !important; }
        td[class='social-icon'] { width: 20px !important; }
        img[class='social-icon'] { width: 20px !important; max-width: 20px !important; height: 20px !important; }
        div[class='spacer-footer'] img { width: 100% !important; height: 1px !important; }
        span[id=logo-switcher] {
        display: inline-block;
        background-image: url('https://i.emlfiles.com/cmpimg/2/3/8/1/1/2/files/791422_logouownalt1x.png') !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        width: 70px !important;
        height: 36px !important;
        }
        img[id=logo-desktop] {display: none !important;}
        }
        \/* END MEDIA QUERY SCREEN 480px */

  %body.body{ style: 'padding:0 !important; margin:0 !important; display:block !important; min-width:100% !important; width:100% !important; background:#F5F3EB; -webkit-text-size-adjust:none' }
    %table{ bgcolor: '#F5F3EB', border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
      %tr
        %td{ align: 'center', valign: 'top' }
          %table.mobile-shell{ bgcolor: '#F5F3EB', border: '0', cellpadding: '0', cellspacing: '0', width: '600' }
            %tr
              %td.td{ bgcolor: '#ffffff', style: 'width:600px; min-width:600px; font-size:0pt; line-height:0pt; padding:0; margin:0; font-weight:normal; Margin:0' }
                = render 'email/header'

                %table{ border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
                  %tr
                    %td{ align: 'center' }
                      %table{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
                        %tr
                          %td{ align: 'center' }
                            %table{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
                              %tr
                                %td.content-gutter{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '60' }  
                                %td
                                  %table{ align: 'center', bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
                                    %tr
                                      %td
                                        = yield

                                %td.content-gutter{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '60' }  

                      %table.spacer{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%', width: '100%' }
                        %tr
                          %td.spacer-h-10{ height: '40', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }  

                      %table.footer-ellipse{ align: 'right', bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', width: '253' }
                        %tr
                          %td{ align: 'center', bgcolor: '#ffffff' }
                            = email_image_tag('<EMAIL>', class: 'footer-ellipse', alt: '', border: '0', style: 'width: 100%; max-width: 253px; text-align: right;', width: '253')

                = render 'email/footer'

          .wgmail{ style: 'font-size:0pt; line-height:0pt; text-align:center' }
            = email_image_tag('gmail_fix.gif', alt: '', border: '0', height: '1', src: 'images/gmail_fix.gif', style: 'min-width:640px', width: '640')
