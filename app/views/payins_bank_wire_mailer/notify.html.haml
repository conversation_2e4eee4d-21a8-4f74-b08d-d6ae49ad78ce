= email_paragraph('Your bank transfer request has been created.  Please use the following details:')

%table{ align: 'center', bgcolor: '#F6F6F0', border: '0', cellpadding: '0', cellspacing: '0', width: '100%' }
  %tr
    %td{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '30' }  
    %td
      %table.spacer{ bgcolor: '#F6F6F0', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%', width: '100%' }
        %tr
          %td.spacer{ height: '20', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }  

      = list_item('Amount', currency(@payin_bank_wire.amount))
      = list_item('Name', @payin_bank_wire.owner_name)
      = list_item('Account Number', Mango::BankWire::UK_ACCOUNT_NUMBER)
      = list_item('Sort Code', Mango::BankWire::UK_SORT_CODE)
      = list_item('Reference', @payin_bank_wire.reference)

    %td{ style: 'font-size:0pt; line-height:0pt; text-align:left', width: '30' }  

%table.spacer{ bgcolor: '#ffffff', border: '0', cellpadding: '0', cellspacing: '0', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%', width: '100%' }
  %tr
    %td.spacer{ height: '20', style: 'font-size:0pt; line-height:0pt; text-align:center; width:100%; min-width:100%' }  

= email_paragraph('Please note it may take up to 24 hours for the banks to process your funds and for the money to appear in your UOWN account.', last: true)
