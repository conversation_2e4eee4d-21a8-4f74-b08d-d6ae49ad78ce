# Module: share_sell_order_mailer

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
* `{Method1}`: {description}
* `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
* [Task1]: {brief description}
* [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
* [Task1]: {brief description}
* [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---

---KEY_DEFINITIONS_START---
Key Definitions:
2Kp: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/views/share_sell_order_mailer
2Kp1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/views/share_sell_order_mailer/sold.html.haml
2Kp2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/app/views/share_sell_order_mailer/sold.text.haml
---KEY_DEFINITIONS_END---

last_KEY_edit: Assigned keys: 2Kp, 2Kp1, 2Kp2
last_GRID_edit: Grid content updated (2025-06-10T08:53:30.098874)

---GRID_START---
X 2Kp 2Kp1 2Kp2
2Kp = oxx
2Kp1 = xop
2Kp2 = xpo
---GRID_END---

---mini_tracker_end---
