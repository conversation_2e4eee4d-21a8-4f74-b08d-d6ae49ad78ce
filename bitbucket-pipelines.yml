image: atlassian/default-image:4

definitions:
  services:
    docker:
      memory: 3072

  steps:
    - step: &build-test
        name: Build & Test
        caches:
          - docker
        services:
          - docker
        script:
          # Build Docker Image
          - DOCKER_BUILDKIT=1 docker build -t `source .env && echo $DOCKER_IMAGE` .

          # Run Tests
          - DOCKER_IMAGE=`source .env && echo $DOCKER_IMAGE` docker-compose -f "docker-compose.yml" run --rm tests

pipelines:
  branches:
    '**':
      - step: *build-test

  tags:
    v*:
      - step: *build-test
