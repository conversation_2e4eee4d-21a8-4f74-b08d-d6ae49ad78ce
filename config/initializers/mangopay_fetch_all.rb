module MangoPay
  module H<PERSON><PERSON>alls
    module Fetch
      module ClassMethods
        def fetch_all(id_or_filters = nil)
          id, filters = HTTPCalls::Fetch.parse_id_or_filters(id_or_filters)

          # Initial request
          response_data = MangoPay.request(:get, url(id), {}, filters)

          if filters.key?('total_pages') && filters['total_pages'] > 1
            (2..filters['total_pages']).to_a.each do |page|
              response_data += MangoPay.request(:get, url(id), {}, filters.merge(page: page))
            end
          end

          response_data
        end
      end
    end
  end
end
