module ActiveModel
  module Validations
    class StrengthValidator
      def validate_each(record, attribute, value)
        return unless PasswordStrength.enabled
        strength = options[:using].new(record.send(options[:with]), value,
          :exclude => options[:exclude],
          :record => record
        )
        strength.test
        record.errors.add(attribute, :too_weak, **options) unless PasswordStrength.enabled && strength.valid?(level(record))
      end
    end
  end
end
