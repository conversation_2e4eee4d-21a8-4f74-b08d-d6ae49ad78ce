Sidekiq.configure_server do |config|
  config.redis = { url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/1') }
end

Sidekiq.configure_client do |config|
  config.redis = { url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/1') }
end

Rails.application.config.active_job.queue_adapter = if Rails.env.test?
                                                      :test
                                                    else
                                                      :sidekiq
                                                    end
