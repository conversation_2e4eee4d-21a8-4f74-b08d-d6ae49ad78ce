class CreateUsers < ActiveRecord::Migration[4.2]
  def change
    create_table :users do |t|
      t.string :email
      t.string :password_digest
      t.string :first_name
      t.string :last_name
      t.string :address_1
      t.string :address_2
      t.string :city
      t.string :region
      t.string :post_code
      t.string :country
      t.date :date_of_birth
      t.string :nationality
      t.string :country_of_residence
      t.string :occupation
      t.integer :income_range
      t.string :aasm_state

      t.timestamps null: false
    end
  end
end
