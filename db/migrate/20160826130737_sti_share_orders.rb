class StiShareOrders < ActiveRecord::Migration[4.2]
  def up
    Share::Order.where(kind: 'sell').update_all(kind: 'Share::SellOrder')
    Share::Order.where(kind: 'buy').update_all(kind: 'Share::BuyOrder')
    rename_column :share_orders, :kind, :type
  end

  def down
    Share::Order.where(type: 'sell').update_all(type: 'Share::SellOrder')
    Share::Order.where(type: 'buy').update_all(type: 'Share::BuyOrder')
    rename_column :share_orders, :type, :kind
  end
end
