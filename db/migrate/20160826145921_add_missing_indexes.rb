class AddMissingIndexes < ActiveRecord::Migration[4.2]
  def change
    add_index :certification_questions, :level_id
    add_index :documents, :property_id
    add_index :floorplans, :property_id
    add_index :photos, :property_id
    add_index :properties, :featured
    add_index :properties, :funded
    add_index :roles, :name
    add_index :share_logs, :property_id
    add_index :share_logs, :user_id
    add_index :share_order_transactions, :order_id
    add_index :share_orders, :type
    add_index :share_orders, :aasm_state
    add_index :user_roles, :user_id
    add_index :user_roles, :role_id
    add_index :users, :email
    add_index :users, :mangopay_id
    add_index :users, :authentication_token
    add_index :users, :reset_token
    add_index :users, :aasm_state
  end
end
