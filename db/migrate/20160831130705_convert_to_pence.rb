class ConvertToPence < ActiveRecord::Migration[4.2]
  def up
    Mandate.find_each do |mandate|
      mandate.amount = mandate.amount * 100
      mandate.save
    end

    change_column :mandates, :amount, :integer

    Property.find_each do |property|
      property.target_amount = property.target_amount * 100
      property.save
    end

    change_column :properties, :target_amount, :integer

    # Share::Order.find_each do |share_order|
    #   share_order.total_amount = share_order.total_amount * 100
    #   share_order.save
    # end
    execute "UPDATE share_orders SET total_amount = total_amount * 100"
    change_column :share_orders, :total_amount, :integer
  end

  def down
    change_column :mandates, :amount, :float

    Mandate.find_each do |mandate|
      mandate.amount = mandate.amount.to_f / 100
      mandate.save
    end

    change_column :properties, :target_amount, :float

    Property.find_each do |property|
      property.target_amount = property.target_amount.to_f / 100
      property.save
    end

    change_column :share_orders, :total_amount, :float

    Share::Order.find_each do |share_order|
      share_order.total_amount = share_order.total_amount.to_f / 100
      share_order.save
    end
  end
end
