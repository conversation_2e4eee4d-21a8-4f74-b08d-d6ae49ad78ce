class RecalculatePaymentLogs < ActiveRecord::Migration[4.2]
  def up
    PaymentLog.where(user_id: nil).each do |payment_log|
      payin = MangoPay::PayIn.fetch(payment_log.mangopay_id)

      payment_log.user_id = User.find_by(mangopay_id: payin['UserId']).id
      payment_log.direction = payin['Type'] == 'PAYIN' ? 'credit' : 'debit'

      case payment_log.kind
      when 'CARD', 'DIRECT_DEBIT'
        payment_log.amount = payin['CreditedFunds']['Amount']
      when 'BANK_WIRE'
        payment_log.amount = payin['DeclaredDebitedFunds']['Amount']
      end

      payment_log.save

      print '.'
    end

    PaymentLog.where(kind: 'CARD').each { |pl| pl.successful!('SUCCEEDED') }
  end

  def down
  end
end
