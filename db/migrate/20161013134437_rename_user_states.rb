class RenameUserStates < ActiveRecord::Migration[4.2]
  def up
    User.where(aasm_state: 'kyc_is_light').update_all(aasm_state: 'kyc_light_is_complete')
    User.where(aasm_state: 'kyc_is_required').update_all(aasm_state: 'kyc_regular_required')
    User.where(aasm_state: 'kyc_is_pending').update_all(aasm_state: 'kyc_regular_pending')
    User.where(aasm_state: 'kyc_is_complete').update_all(aasm_state: 'kyc_regular_complete')
    User.where(aasm_state: 'kyc_has_failed').update_all(aasm_state: 'kyc_regular_has_failed')
  end

  def down
    User.where(aasm_state: 'kyc_light_is_complete').update_all(aasm_state: 'kyc_is_light')
    User.where(aasm_state: 'kyc_regular_required').update_all(aasm_state: 'kyc_is_required')
    User.where(aasm_state: 'kyc_regular_pending').update_all(aasm_state: 'kyc_is_pending')
    User.where(aasm_state: 'kyc_regular_complete').update_all(aasm_state: 'kyc_is_complete')
    User.where(aasm_state: 'kyc_regular_has_failed').update_all(aasm_state: 'kyc_has_failed')
  end
end
