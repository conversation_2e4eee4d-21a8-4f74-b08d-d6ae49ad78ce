class FixBadUserStates < ActiveRecord::Migration[4.2]
  def up
    User.where(aasm_state: 'kyc_regular_required').update_all(aasm_state: 'kyc_regular_is_required')
    User.where(aasm_state: 'kyc_regular_pending').update_all(aasm_state: 'kyc_regular_is_pending')
    User.where(aasm_state: 'kyc_regular_complete').update_all(aasm_state: 'kyc_regular_is_complete')
  end

  def down
    User.where(aasm_state: 'kyc_regular_is_required').update_all(aasm_state: 'kyc_regular_required')
    User.where(aasm_state: 'kyc_regular_is_pending').update_all(aasm_state: 'kyc_regular_pending')
    User.where(aasm_state: 'kyc_regular_is_complete').update_all(aasm_state: 'kyc_regular_complete')
  end
end
