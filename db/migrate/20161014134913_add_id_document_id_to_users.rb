class AddIdDocumentIdToUsers < ActiveRecord::Migration[4.2]
  def up
    add_column :users, :kyc_document_id, :integer

    User.kyc_regular_is_pending.each do |user|
      kyc_documents = MangoPay::KycDocument.fetch_all(user.mangopay_id)

      if kyc_documents.any?
        user.kyc_document_id = kyc_documents.first['Id']
        user.save(validate: false)
        print '+'
      else
        print '-'
      end
    end
  end

  def down
    remove_column :users, :kyc_document_id, :integer
  end
end
