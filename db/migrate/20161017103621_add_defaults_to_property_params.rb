class AddDefaultsToPropertyParams < ActiveRecord::Migration[4.2]
  def change
    change_column_default :properties, :property_fee_stamp_duty, 0
    change_column_default :properties, :property_fee_legal_and_professional, 0
    change_column_default :properties, :property_fee_pre_let_expenses, 0
    change_column_default :properties, :property_fee_repairs_provision, 0
    change_column_default :properties, :property_fee_deferred_tax, 0
    change_column_default :properties, :rental_fee_management, 0
    change_column_default :properties, :rental_fee_insurance, 0
    change_column_default :properties, :rental_fee_allowance_for_voids, 0
    change_column_default :properties, :rental_fee_maintenance_allowance, 0
    change_column_default :properties, :rental_fee_corporation_tax, 0
    change_column_default :properties, :rental_fee_deferred_fees, 0
  end
end
