class AddWalletIdToUsers < ActiveRecord::Migration[4.2]
  def up
    add_column :users, :wallet_id, :integer

    User.find_each do |user|
      next unless user.mangopay_id.present?

      wallets = Mango::Wallet.find_by_user(user)
      wallet = wallets.find { |w| w.description == Mango::Wallet::DEFAULT_WALLET_NAME }

      user.update_column(:wallet_id, wallet.id) if wallet

      print '.'
    end
  end

  def down
    remove_column :users, :wallet_id
  end
end
