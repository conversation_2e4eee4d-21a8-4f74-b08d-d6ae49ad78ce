class AddBusinessFieldsToUser < ActiveRecord::Migration[4.2]
  def up
    add_column :users, :type, :string
    add_column :users, :legal_type, :string
    add_column :users, :business_name, :string
    add_column :users, :headquarters_address_1, :string
    add_column :users, :headquarters_address_2, :string
    add_column :users, :headquarters_city, :string
    add_column :users, :headquarters_region, :string
    add_column :users, :headquarters_post_code, :string
    add_column :users, :headquarters_country, :string

    User.update_all(type: 'User::Natural')
  end

  def down
    remove_column :users, :type
    remove_column :users, :legal_type
    remove_column :users, :business_name
    remove_column :users, :headquarters_address_1
    remove_column :users, :headquarters_address_2
    remove_column :users, :headquarters_city
    remove_column :users, :headquarters_region
    remove_column :users, :headquarters_post_code
    remove_column :users, :headquarters_country
  end
end
