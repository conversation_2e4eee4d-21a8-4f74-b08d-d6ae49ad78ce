class CreateUserKycDocuments < ActiveRecord::Migration[4.2]
  def up
    create_table :user_kyc_documents do |t|
      t.references :user, index: true, foreign_key: true
      t.integer :kyc_document_id
      t.string :kind
      t.string :state

      t.timestamps null: false
    end

    User::Natural.where.not(kyc_document_id: nil).each do |user|
      User::KycDocument.create!(user_id: user.id,
                                kyc_document_id: user.kyc_document_id,
                                kind: 'identity_proof')
    end

    User::KycDocument.find_each(&:update_status)

    remove_column :users, :kyc_document_id
  end

  def down
    add_column :users, :kyc_document_id, :integer
    drop_table :user_kyc_documents
  end
end
