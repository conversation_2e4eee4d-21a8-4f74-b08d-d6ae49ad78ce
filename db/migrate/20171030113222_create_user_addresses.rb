class CreateUserAddresses < ActiveRecord::Migration[4.2]
  def change
    create_table :user_addresses do |t|
      t.references :user, index: true, foreign_key: true
      t.string :first_name
      t.string :last_name
      t.string :address_1
      t.string :city
      t.string :region
      t.string :post_code
      t.string :country
      t.string :kind
      t.date :date_of_birth

      t.timestamps null: false
    end
  end
end
