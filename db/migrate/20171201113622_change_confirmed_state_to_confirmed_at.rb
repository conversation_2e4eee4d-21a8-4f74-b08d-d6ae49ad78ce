class ChangeConfirmedStateToConfirmedAt < ActiveRecord::Migration[4.2]
  def up
    User.where(aasm_state: 'pending').update_all(aasm_state: 'registered')

    User.where.not(aasm_state: 'registered').find_each do |user|
      state_change = user.user_states.find_by(after: 'confirmed')
      date_confirmed = state_change.present? ? state_change.created_at : Time.now

      user.update_column(:confirmed_at, date_confirmed)
    end

    User.where(aasm_state: 'confirmed').update_all(aasm_state: 'registered')
  end

  def down
    User.where(aasm_state: 'registered').update_all(aasm_state: 'confirmed')
    User.where(confirmed_at: nil).update_all(aasm_state: 'pending')
  end
end
