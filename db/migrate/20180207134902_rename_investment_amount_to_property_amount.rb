class RenameInvestmentAmountToPropertyAmount < ActiveRecord::Migration[5.0]
  def up
    rename_column :properties, :target_amount, :property_amount

    # Investment Value = Property Price + Stamp duty + Legal and professional + Pre let expenses
    #                  + Repairs provision - Deferred tax
    Property.find_each do |p|
      property_amount = p.property_amount -
                        p.property_fee_stamp_duty -
                        p.property_fee_legal_and_professional -
                        p.property_fee_pre_let_expenses -
                        p.property_fee_repairs_provision +
                        p.property_fee_deferred_tax

      p.update_column(:property_amount, property_amount)
    end
  end

  def down
    rename_column :properties, :property_amount, :target_amount

    Property.find_each do |p|
      p.update_column(:target_amount, p.target_amount)
    end
  end
end
