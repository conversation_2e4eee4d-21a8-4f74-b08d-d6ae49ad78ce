class CreatePaymentLogsForDividends < ActiveRecord::Migration[5.0]
  def up
    Property::Dividend.complete.each do |dividend|
      pl = PaymentLog.new(user: dividend.user,
                          dividend_id: dividend.id,
                          amount: dividend.amount.to_i,
                          kind: 'DIVIDEND',
                          direction: 'credit',
                          status: 'SUCCEEDED',
                          successful_at: dividend.created_at,
                          created_at: dividend.created_at,
                          updated_at: dividend.updated_at)

      # We skip validation here because we don't
      # have the mangopay id for these legacy transactions
      pl.save(validate: false)
    end
  end

  def down
    # Do Nothing
  end
end
