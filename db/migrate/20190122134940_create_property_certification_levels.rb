class CreatePropertyCertificationLevels < ActiveRecord::Migration[5.2]
  def up
    create_table :property_certification_levels do |t|
      t.references :property
      t.references :certification_level
      t.timestamps
    end

    # Add all properties to all levels for now
    ids = Certification::Level.ids

    Property.find_each do |property|
      property.certification_level_ids = ids
      property.save
    end
  end

  def down
    drop_table :property_certification_levels
  end
end
