class CreatePropertyLegalDocuments < ActiveRecord::Migration[5.2]
  def change
    create_table :property_legal_documents do |t|
      t.integer :property_id
      t.string :title

      t.timestamps

      t.string :attachment_file_name
      t.string :attachment_content_type
      t.bigint :attachment_file_size
      t.datetime :attachment_updated_at
    end

    add_index :property_legal_documents, :property_id
  end
end
