class ReworkRentPayments < ActiveRecord::Migration[5.2]
  def up
    rename_table :property_rent_payments, :property_payouts
    add_column :property_payouts, :type, :string
    rename_column :property_dividends, :rent_payment_id, :payout_id

    Property::Payout.update_all(type: 'Property::PayoutRent')
  end

  def down
    rename_column :property_dividends, :payout_id, :rent_payment_id
    remove_column :property_payouts, :type, :string
    rename_table :property_payouts, :property_rent_payments
  end
end
