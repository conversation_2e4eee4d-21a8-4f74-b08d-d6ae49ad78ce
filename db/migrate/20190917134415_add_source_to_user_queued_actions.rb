class AddSourceToUserQueuedActions < ActiveRecord::Migration[5.2]
  def up
    add_reference :user_queued_actions, :source, polymorphic: true

    PaymentLog.where(kind: 'BANK_WIRE').each do |payment_log|
      state = nil
      state = 'completed' if payment_log.successful_at
      state = 'failed' if payment_log.failed_at

      User::QueuedActionInvestment.create(source: payment_log,
                                          target: payment_log.potential_investment,
                                          user: payment_log.user,
                                          created_at: payment_log.created_at,
                                          updated_at: payment_log.updated_at,
                                          aasm_state: state)
    end
  end

  def down
    remove_reference :user_queued_actions, :source, polymorphic: true
  end
end
