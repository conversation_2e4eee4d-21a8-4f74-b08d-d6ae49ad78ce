class MoveUserAddresses < ActiveRecord::Migration[5.2]
  def up
    add_column :user_addresses, :address_2, :string, after: :address_1

    User::Address.transaction do
      User.where.not(aasm_state: 'registered').find_each do |user|
        User::Address.new(user: user,
                          kind: User::Address::PERSONAL,
                          address_1: user.address_1,
                          address_2: user.address_2,
                          city: user.city,
                          region: user.region,
                          post_code: user.post_code,
                          country: user.country)
                     .save(validate: false)

        next unless user.is_a?(User::Legal)

        User::Address.new(user: user,
                          kind: User::Address::HEADQUARTERS,
                          address_1: user.headquarters_address_1,
                          address_2: user.headquarters_address_2,
                          city: user.headquarters_city,
                          region: user.headquarters_region,
                          post_code: user.headquarters_post_code,
                          country: user.headquarters_country)
                     .save(validate: false)
      end
    end

    remove_column :users, :address_1
    remove_column :users, :address_2
    remove_column :users, :city
    remove_column :users, :region
    remove_column :users, :post_code
    remove_column :users, :country
    remove_column :users, :headquarters_address_1
    remove_column :users, :headquarters_address_2
    remove_column :users, :headquarters_city
    remove_column :users, :headquarters_region
    remove_column :users, :headquarters_post_code
    remove_column :users, :headquarters_country
  end

  def down
    add_column :users, :address_1, :string
    add_column :users, :address_2, :string
    add_column :users, :city, :string
    add_column :users, :region, :string
    add_column :users, :post_code, :string
    add_column :users, :country, :string
    add_column :users, :headquarters_address_1, :string
    add_column :users, :headquarters_address_2, :string
    add_column :users, :headquarters_city, :string
    add_column :users, :headquarters_region, :string
    add_column :users, :headquarters_post_code, :string
    add_column :users, :headquarters_country, :string

    User::Address.transaction do
      User::Address.where(kind: User::Address::PERSONAL).includes(:user).each do |address|
        address.user.update_columns(address_1: address.address_1,
                                    address_2: address.address_2,
                                    city: address.city,
                                    region: address.region,
                                    post_code: address.post_code,
                                    country: address.country)
      end

      User::Address.where(kind: User::Address::HEADQUARTERS).includes(:user).each do |address|
        address.user.update_columns(headquarters_address_1: address.address_1,
                                    headquarters_address_2: address.address_2,
                                    headquarters_city: address.city,
                                    headquarters_region: address.region,
                                    headquarters_post_code: address.post_code,
                                    headquarters_country: address.country)
      end
    end

    remove_column :user_addresses, :address_2
  end
end
