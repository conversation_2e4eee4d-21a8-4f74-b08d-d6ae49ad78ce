require 'indirizzo/address'

class SplitAddress1 < ActiveRecord::Migration[5.2]
  def up
    add_column :user_addresses, :address_number, :string, after: :last_name

    User::Address.find_each do |address|
      indirizzo = Indirizzo::Address.new(address.address_1) if address.address_1.present?

      next unless indirizzo

      address.update_columns(address_number: indirizzo.number&.titleize_with_respect, address_1: indirizzo.street&.first&.titleize_with_respect)
    end
  end

  def down
    User::Address.find_each do |address|
      address.update_columns(address_1: [address.address_number, address.address_1].join(' '))
    end

    remove_column :user_addresses, :address_number
  end
end
