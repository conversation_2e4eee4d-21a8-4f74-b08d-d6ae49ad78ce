class MakeWalletIdsStrings < ActiveRecord::Migration[5.2]
  def up
    change_column :properties, :wallet_id, :string
    change_column :property_payouts, :wallet_id, :string
    change_column :share_orders, :wallet_id, :string
    change_column :users, :wallet_id, :string
  end

  def down
    change_column :properties, :wallet_id, :integer
    change_column :property_payouts, :wallet_id, :integer
    change_column :share_orders, :wallet_id, :integer
    change_column :users, :wallet_id, :integer
  end
end
