class CreateRemoteBankAccounts < ActiveRecord::Migration[7.0]
  def change
    create_table :remote_bank_accounts do |t|
      t.string :remote_id
      t.string :owner_name
      t.string :bank_name
      t.string :iban
      t.string :account_number
      t.string :bank_code
      t.string :branch_code
      t.string :check_sum
      t.string :account_country
      t.string :bic
      t.string :status
      t.string :account_type
      t.references :bankable, polymorphic: true, index: true

      t.timestamps
    end
  end
end
