# Module: migrate

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
* `{Method1}`: {description}
* `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
* [Task1]: {brief description}
* [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
* [Task1]: {brief description}
* [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---

---KEY_DEFINITIONS_START---
Key Definitions:
2A#4: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate
2A1#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160217152448_create_users.rb
2A2#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160217152450_create_properties.rb
2A3#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160217152452_create_documents.rb
2A4#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160217152455_create_photos.rb
2A5#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160217152457_create_floorplans.rb
2A6#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160219121113_add_authentication_token_to_users.rb
2A7#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160219133020_create_roles.rb
2A8#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160219133037_create_user_roles.rb
2A9#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160314155338_add_confirmation_token_to_users.rb
2A10#1: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160510135412_add_mangopay_id_to_users.rb
2A11: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160513103657_create_certification_levels.rb
2A12: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160513103752_create_certification_questions.rb
2A13: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160513104219_add_certification_level_id_to_users.rb
2A14: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160616092215_add_reset_token_to_users.rb
2A15: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160721141236_create_share_transactions.rb
2A16: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160804104502_add_reason_to_share_transactions.rb
2A17: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160809153113_add_indexes_to_share_transactions.rb
2A18: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160810160627_create_share_logs.rb
2A19: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160811134553_rework_share_transactions.rb
2A20: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160811140113_create_share_order_transactions.rb
2A21: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160811152704_add_funded_to_property.rb
2A22: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160811153428_add_wallet_id_to_properties.rb
2A23: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160819122220_create_mandates.rb
2A24: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160824133845_create_payin_logs.rb
2A25: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160824151700_add_last_payment_at_to_mandates.rb
2A26: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160824154440_add_cancelled_at_to_mandates.rb
2A27: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160825133929_rework_share_log_columns.rb
2A28: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160826130737_sti_share_orders.rb
2A29: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160826145921_add_missing_indexes.rb
2A30: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160831130705_convert_to_pence.rb
2A31: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160905145441_add_user_reference_to_payin_logs.rb
2A32: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160905145648_rename_payin_logs_to_payment_logs.rb
2A33: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160907122257_add_direction_and_amount_to_payment_logs.rb
2A34: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160907124239_recalculate_payment_logs.rb
2A35: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160913154651_fix_existing_kyc_light_users.rb
2A36: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160915151654_rename_kyc_is_regular.rb
2A37: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160921101802_create_property_rent_payments.rb
2A38: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160921101827_create_property_rent_payment_items.rb
2A39: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160921101836_create_property_dividends.rb
2A40: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160926144457_rename_buy_order_states.rb
2A41: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20160929095524_add_state_machine_to_rent_payments.rb
2A42: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161003104143_add_state_machine_to_property_dividends.rb
2A43: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161011140545_add_title_to_users.rb
2A44: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161013134437_rename_user_states.rb
2A45: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161013151653_create_user_states.rb
2A46: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161014090718_fix_bad_user_states.rb
2A47: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161014134913_add_id_document_id_to_users.rb
2A48: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161014162407_add_property_fees_to_properties.rb
2A49: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161014162817_add_rental_fees_to_properties.rb
2A50: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161017103621_add_defaults_to_property_params.rb
2A51: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161019085048_add_total_amount_to_share_orders.rb
2A52: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161020155031_add_rent_amount_to_properties.rb
2A53: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161024122311_add_position_and_default_to_certification_levels.rb
2A54: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161027105524_create_property_tags.rb
2A55: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161027105558_create_property_property_tags.rb
2A56: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161028085419_rename_property_association_tables.rb
2A57: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161028093813_create_investment_documents.rb
2A58: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161031132833_add_wallet_id_to_users.rb
2A59: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161103102421_add_investment_case_and_risk_to_properties.rb
2A60: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161108103206_remove_redundant_fields.rb
2A61: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161122134345_add_visible_to_properties.rb
2A62: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161128165813_add_spv_name_to_properties.rb
2A63: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161130132110_add_spv_charge_to_properties.rb
2A64: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20161213154315_add_hpi_area_to_properties.rb
2A65: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20170221135701_add_placeholder_to_properties.rb
2A66: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20170329121222_add_start_end_date_to_property_rent_payments.rb
2A67: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20170516091326_add_transfer_fields_to_share_orders.rb
2A68: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20170518092226_add_description_to_share_orders.rb
2A69: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20170518131722_add_missing_indexes_to_share_orders.rb
2A70: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20170713120600_add_business_fields_to_user.rb
2A71: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20170920152056_create_user_kyc_documents.rb
2A72: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171023132833_add_kind_to_cerfication_levels.rb
2A73: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171030112954_add_company_number_to_users.rb
2A74: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171030113222_create_user_addresses.rb
2A75: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171116151509_add_cancelled_at_to_share_orders.rb
2A76: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171129124917_add_call_me_boolean_to_users.rb
2A77: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171129161605_add_confirmed_at_datetime_to_users.rb
2A78: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/***********622_change_confirmed_state_to_confirmed_at.rb
2A79: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171204141034_add_date_to_mandates.rb
2A80: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171204143020_rework_mandate_last_payment_at.rb
2A81: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171204154526_add_brief_to_certification_level.rb
2A82: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171204170647_add_attachment_to_certification_levels.rb
2A83: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171206104942_create_potential_investments.rb
2A84: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171207132705_create_potential_investment_items.rb
2A85: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20171208094641_add_potential_investment_id_to_payment_logs.rb
2A86: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180110163457_add_authy_id_to_users.rb
2A87: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180110165512_add_authy_number_to_users.rb
2A88: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180111170829_remove_answer_from_certification_questions.rb
2A89: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180111170853_create_certification_answers.rb
2A90: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180112150847_add_additional_user_fields.rb
2A91: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180129150545_add_two_factor_authentication_token_to_users.rb
2A92: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180207134902_rename_investment_amount_to_property_amount.rb
2A93: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180214114459_change_dividends_amount_to_decimal.rb
2A94: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180214134715_add_dividend_id_to_payment_logs.rb
2A95: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180214151359_create_payment_logs_for_dividends.rb
2A96: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180319144031_add_easy_exit_to_properties.rb
2A97: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180409150239_add_guaranteed_yield_to_properties.rb
2A98: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180802072732_add_column_marketing_to_users.rb
2A99: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180828085127_add_slug_to_properties.rb
2A100: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180830170435_change_call_me_default_to_false.rb
2A101: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180831110722_add_address_columns_to_properties.rb
2A102: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180905122257_add_type_to_properties.rb
2A103: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180910123555_add_additional_development_fields_to_properties.rb
2A104: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180911101559_add_additional_development_fields.rb
2A105: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20180919094848_create_user_queued_actions.rb
2A106: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20181109115651_add_label_names_to_property.rb
2A107: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190107143002_add_wallet_id_to_property_rent_payments.rb
2A108: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190122134940_create_property_certification_levels.rb
2A109: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190124165237_create_property_legal_documents.rb
2A110: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190204095622_create_property_news_items.rb
2A111: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190207132138_change_annualised_return_default.rb
2A112: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190306152603_add_term_to_properties.rb
2A113: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190312143241_create_user_login_attempts.rb
2A114: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190312151146_add_locked_at_to_users.rb
2A115: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190409152651_add_total_fees_to_share_logs.rb
2A116: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190423151452_create_user_certification_attempts.rb
2A117: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190510092659_add_notified_at_to_user_states.rb
2A118: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190514090550_rework_rent_payments.rb
2A119: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190514120119_rework_rent_payment_items.rb
2A120: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190515125843_add_payout_id_to_share_orders.rb
2A121: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190516144547_add_mangopay_id_to_share_orders.rb
2A122: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190520094843_add_idempotency_key_to_property_dividends.rb
2A123: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190617150318_update_property_dividends_amount.rb
2A124: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190723134126_add_onfido_id_to_users.rb
2A125: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190729093237_add_type_to_user_kyc_documents.rb
2A126: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190730153613_change_user_kyc_documents_kyc_document_id.rb
2A127: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190814113125_add_referal_fields_to_users.rb
2A128: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190910083118_change_guaranteed_yield_on_properties_to_decimal.rb
2A129: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20190917134415_add_source_to_user_queued_actions.rb
2A130: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20191011152800_move_user_addresses.rb
2A131: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20191015135121_split_address_1.rb
2A132: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20191021095847_add_last_active_at_to_users.rb
2A133: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20200923105851_update_payout_type.rb
2A134: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20210412082826_remove_failed_watch_list_check_user_state.rb
2A135: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20210415083703_add_thumbnail_label_to_properties.rb
2A136: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20210415154210_add_attachment_to_properties.rb
2A137: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20211112115410_create_active_storage_tables.active_storage.rb
2A138: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20211123104607_fix_property_guaranteed_yield_precision.rb
2A139: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20220131114242_change_mangopay_ids_to_strings.rb
2A140: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20220201105401_make_wallet_ids_strings.rb
2A141: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20220201114447_change_bank_account_ids_to_strings.rb
2A142: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240322070835_create_noticed_tables.noticed.rb
2A143: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240322070836_add_notifications_count_to_noticed_event.noticed.rb
2A144: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240328061750_add_approved_to_user.rb
2A145: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240403095046_add_payout_threshold_approved_to_users.rb
2A146: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240423101024_add_telnyx_id_to_users.rb
2A147: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240428035217_add_factor_to_users.rb
2A148: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240528101653_add_email_octopus_id_to_users.rb
2A149: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240607085500_change_email_octopus_id_to_string.rb
2A150: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240614113643_add_flg_lead_id_to_users.rb
2A151: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240712043107_add_seen_at_to_noticed_events.rb
2A152: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240820144124_add_google_uid_to_users.rb
2A153: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20240903092535_remove_approved_and_threshold.rb
2A154: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20241015133310_add_apple_uid_to_users.rb
2A155: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20250415124058_add_sca_status_to_users.rb
2A156: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20250520115627_rename_mangopay_id_to_remote_id_in_payment_logs.rb
2A157: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20250526100507_add_verification_url_to_user_kyc_documents.rb
2A158: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20250529134918_create_remote_bank_accounts.rb
2A159: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/db/migrate/20250530103540_add_processing_to_property_dividends.rb
---KEY_DEFINITIONS_END---

last_KEY_edit: Assigned keys: 2A, 2A1, 2A1, 2A1, 2A1, 2A2, 2A2, 2A2, 2A2, 2A3, 2A3, 2A3, 2A3, 2A4, 2A4, 2A4, 2A5, 2A5, 2A6, 2A6, 2A7, 2A7, 2A8, 2A8, 2A9, 2A9, 2A10, 2A10, 2A11, 2A12, 2A13, 2A14, 2A15, 2A16, 2A17, 2A18, 2A19, 2A20, 2A21, 2A22, 2A23, 2A24, 2A25, 2A26, 2A27, 2A28, 2A29, 2A30, 2A31, 2A32, 2A33, 2A34, 2A35, 2A36, 2A37, 2A38, 2A39, 2A40, 2A41, 2A42, 2A43, 2A44, 2A45, 2A46, 2A47, 2A48, 2A49, 2A50, 2A51, 2A52, 2A53, 2A54, 2A55, 2A56, 2A57, 2A58, 2A59, 2A60, 2A61, 2A62, 2A63, 2A64, 2A65, 2A66, 2A67, 2A68, 2A69, 2A70, 2A71, 2A72, 2A73, 2A74, 2A75, 2A76, 2A77, 2A78, 2A79, 2A80, 2A81, 2A82, 2A83, 2A84, 2A85, 2A86, 2A87, 2A88, 2A89, 2A90, 2A91, 2A92, 2A93, 2A94, 2A95, 2A96, 2A97, 2A98, 2A99, 2A100, 2A101, 2A102, 2A103, 2A104, 2A105, 2A106, 2A107, 2A108, 2A109, 2A110, 2A111, 2A112, 2A113, 2A114, 2A115, 2A116, 2A117, 2A118, 2A119, 2A120, 2A121, 2A122, 2A123, 2A124, 2A125, 2A126, 2A127, 2A128, 2A129, 2A130, 2A131, 2A132, 2A133, 2A134, 2A135, 2A136, 2A137, 2A138, 2A139, 2A140, 2A141, 2A142, 2A143, 2A144, 2A145, 2A146, 2A147, 2A148, 2A149, 2A150, 2A151, 2A152, 2A153, 2A154, 2A155, 2A156, 2A157, 2A158, 2A159
last_GRID_edit: Grid structure updated (2025-06-10T09:17:39.849893)

---GRID_START---
X 2A#4 2A1#2 2A2#2 2A3#2 2A4#2 2A5#1 2A6#1 2A7#1 2A8#1 2A9#1 2A10#1 2A11 2A12 2A13 2A14 2A15 2A16 2A17 2A18 2A19 2A20 2A21 2A22 2A23 2A24 2A25 2A26 2A27 2A28 2A29 2A30 2A31 2A32 2A33 2A34 2A35 2A36 2A37 2A38 2A39 2A40 2A41 2A42 2A43 2A44 2A45 2A46 2A47 2A48 2A49 2A50 2A51 2A52 2A53 2A54 2A55 2A56 2A57 2A58 2A59 2A60 2A61 2A62 2A63 2A64 2A65 2A66 2A67 2A68 2A69 2A70 2A71 2A72 2A73 2A74 2A75 2A76 2A77 2A78 2A79 2A80 2A81 2A82 2A83 2A84 2A85 2A86 2A87 2A88 2A89 2A90 2A91 2A92 2A93 2A94 2A95 2A96 2A97 2A98 2A99 2A100 2A101 2A102 2A103 2A104 2A105 2A106 2A107 2A108 2A109 2A110 2A111 2A112 2A113 2A114 2A115 2A116 2A117 2A118 2A119 2A120 2A121 2A122 2A123 2A124 2A125 2A126 2A127 2A128 2A129 2A130 2A131 2A132 2A133 2A134 2A135 2A136 2A137 2A138 2A139 2A140 2A141 2A142 2A143 2A144 2A145 2A146 2A147 2A148 2A149 2A150 2A151 2A152 2A153 2A154 2A155 2A156 2A157 2A158 2A159
2A#4 = ox159
2A1#2 = xop158
2A2#2 = xpop157
2A3#2 = xppop156
2A4#2 = xp3op155
2A5#1 = xp4op154
2A6#1 = xp5op153
2A7#1 = xp6op152
2A8#1 = xp7op151
2A9#1 = xp8op150
2A10#1 = xp9op149
2A11 = xp10op148
2A12 = xp11op147
2A13 = xp12op146
2A14 = xp13op145
2A15 = xp14op144
2A16 = xp15op143
2A17 = xp16op142
2A18 = xp17op141
2A19 = xp18op140
2A20 = xp19op139
2A21 = xp20op138
2A22 = xp21op137
2A23 = xp22op136
2A24 = xp23op135
2A25 = xp24op134
2A26 = xp25op133
2A27 = xp26op132
2A28 = xp27op131
2A29 = xp28op130
2A30 = xp29op129
2A31 = xp30op128
2A32 = xp31op127
2A33 = xp32op126
2A34 = xp33op125
2A35 = xp34op124
2A36 = xp35op123
2A37 = xp36op122
2A38 = xp37op121
2A39 = xp38op120
2A40 = xp39op119
2A41 = xp40op118
2A42 = xp41op117
2A43 = xp42op116
2A44 = xp43op115
2A45 = xp44op114
2A46 = xp45op113
2A47 = xp46op112
2A48 = xp47op111
2A49 = xp48op110
2A50 = xp49op109
2A51 = xp50op108
2A52 = xp51op107
2A53 = xp52op106
2A54 = xp53op105
2A55 = xp54op104
2A56 = xp55op103
2A57 = xp56op102
2A58 = xp57op101
2A59 = xp58op100
2A60 = xp59op99
2A61 = xp60op98
2A62 = xp61op97
2A63 = xp62op96
2A64 = xp63op95
2A65 = xp64op94
2A66 = xp65op93
2A67 = xp66op92
2A68 = xp67op91
2A69 = xp68op90
2A70 = xp69op89
2A71 = xp70op88
2A72 = xp71op87
2A73 = xp72op86
2A74 = xp73op85
2A75 = xp74op84
2A76 = xp75op83
2A77 = xp76op82
2A78 = xp77op81
2A79 = xp78op80
2A80 = xp79op79
2A81 = xp80op78
2A82 = xp81op77
2A83 = xp82op76
2A84 = xp83op75
2A85 = xp84op74
2A86 = xp85op73
2A87 = xp86op72
2A88 = xp87op71
2A89 = xp88op70
2A90 = xp89op69
2A91 = xp90op68
2A92 = xp91op67
2A93 = xp92op66
2A94 = xp93op65
2A95 = xp94op64
2A96 = xp95op63
2A97 = xp96op62
2A98 = xp97op61
2A99 = xp98op60
2A100 = xp99op59
2A101 = xp100op58
2A102 = xp101op57
2A103 = xp102op56
2A104 = xp103op55
2A105 = xp104op54
2A106 = xp105op53
2A107 = xp106op52
2A108 = xp107op51
2A109 = xp108op50
2A110 = xp109op49
2A111 = xp110op48
2A112 = xp111op47
2A113 = xp112op46
2A114 = xp113op45
2A115 = xp114op44
2A116 = xp115op43
2A117 = xp116op42
2A118 = xp117op41
2A119 = xp118op40
2A120 = xp119op39
2A121 = xp120op38
2A122 = xp121op37
2A123 = xp122op36
2A124 = xp123op35
2A125 = xp124op34
2A126 = xp125op33
2A127 = xp126op32
2A128 = xp127op31
2A129 = xp128op30
2A130 = xp129op29
2A131 = xp130op28
2A132 = xp131op27
2A133 = xp132op26
2A134 = xp133op25
2A135 = xp134op24
2A136 = xp135op23
2A137 = xp136op22
2A138 = xp137op21
2A139 = xp138op20
2A140 = xp139op19
2A141 = xp140op18
2A142 = xp141op17
2A143 = xp142op16
2A144 = xp143op15
2A145 = xp144op14
2A146 = xp145op13
2A147 = xp146op12
2A148 = xp147op11
2A149 = xp148op10
2A150 = xp149op9
2A151 = xp150op8
2A152 = xp151op7
2A153 = xp152op6
2A154 = xp153op5
2A155 = xp154op4
2A156 = xp155op3
2A157 = xp156opp
2A158 = xp157op
2A159 = xp158o
---GRID_END---

---mini_tracker_end---
