# NOTE: YAML inheritance is NOT recursive, so inheriting from a top-level service definition and then
#       re-defining a top-level attribute _overrides it completely_ rather than merging in the new values.
#
#       It also works with hashes only (not arrays).
#
# NOTE: Default ENV Var values are set in .env, but can be overridden on the command line or by ENV vars in
#       in the shell session.
services:

  # Abstract services - not intended to be invoked directly, but inherited from by other services.
  # These must be defined first in order for the YAML anchors to work.
  ############################################################################################################

  # Abstract service all other Rails development services inherit from
  abstract-env: &abstract_env
    image: ${DOCKER_IMAGE}
    # We need tty to get the Rails server's logs (which are being output to StdOut)
    tty: true
    # Mount the current directory as the app's source files (overriding/hiding the container source files)
    # and mount volumes where we expect a lot of I/O to occur that we don't want saved to the host filesystem
    volumes:
      - .:/home/<USER>/webapp:cached

  # Concrete services - invoke with `docker-compose up <service-name>` for background or daemon services and
  # `docker-compose run <service-name>` for services you wish to interact with
  ############################################################################################################

  # Tests
  abstract-test-env: &abstract_test_env
    <<: *abstract_env
    environment:
      RAILS_ENV: test

  # Opens a bash console suitable for running commands that affect the test environment
  test-console:
    <<: *abstract_test_env
    command: /bin/bash

  # Runs the application's test suite
  tests: &tests
    <<: *abstract_test_env
    command: bash -c 'bundle install && rake db:migrate && bundle exec rspec'

  # Runs only the specs that failed in the last testsuite execution
  tests-only-failures:
    <<: *abstract_test_env
    command: bash -c 'rake db:migrate && bundle exec rspec --only-failures'
