module DatabaseGrants
  class << self
    def grant_permission(user, table, permissions = [:insert])
      return false unless is_mysql?

      ActiveRecord::Migration.execute("GRANT #{formatted_permissions(permissions)} ON `#{database_configuration['database']}`.#{formatted_table(table)} TO '#{user}'@'%'")
    end

    def revoke_permission(user, table, permissions = [:insert])
      return false unless is_mysql?

      ActiveRecord::Migration.execute("REVOKE #{formatted_permissions(permissions)} ON `#{database_configuration['database']}`.#{formatted_table(table)} FROM '#{user}'@'%'")
    end

    private

    def database_configuration
      Rails.configuration.database_configuration[Rails.env]
    end

    def formatted_permissions(permissions = [])
      permissions.collect { |p| p.to_s.upcase }.join(', ')
    end

    def formatted_table(table)
      table == '*' ? '*' : "`#{table}`"
    end

    def is_mysql?
      database_configuration['adapter'] == 'mysql2'
    end
  end
end
