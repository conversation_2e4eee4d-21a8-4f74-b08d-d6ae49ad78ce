# frozen_string_literal: true

module Paperclip
  # Migrate paperclip files in s3 bucket to active storage attachments.
  class TransitionToActiveStorage
    def initialize(model, attachment, source_style: :original)
      @model = model
      @attachment = attachment
      @source_style = source_style
    end

    def execute
      @model.find_each do |instance|
        filename = instance.public_send("#{@attachment}_file_name")
        source_url = paperclip_url(instance)

        next unless filename.present?

        begin
          downloaded_image = URI.open(source_url)

          puts "Migrating: #{source_url}"

          instance.send(@attachment).attach(io: downloaded_image, filename: filename)

          downloaded_image.close
        rescue OpenURI::HTTPError => e
          puts "Failed to download: #{source_url} (#{e.message})"
        end
      end
    end

    private

    def paperclip_url(instance)
      url_parts = [
        bucket,
        path,
        @attachment.to_s.pluralize,
        id_partition(instance),
        @source_style,
        instance.send("#{@attachment}_file_name")
      ]

      url_parts.join('/')
    end

    def id_partition(instance)
      instance.id
              .to_s
              .rjust(9, '0')
              .scan(/.../)
              .join('/')
    end

    def bucket
      ActiveStorage::Blob.new.service.bucket.url
    end

    def path
      @model.to_s.pluralize.gsub('::', '/').underscore
    end
  end
end
