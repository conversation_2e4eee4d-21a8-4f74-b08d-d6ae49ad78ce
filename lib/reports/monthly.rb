module Reports
  class Monthly
    def initialize(start_date:, end_date:)
      @start_date = start_date
      @end_date = end_date
    end

    def kpis
      {
        monthly_invested_amount: monthly_invested_amount,
        sign_ups_count: sign_up_user_ids.length,
        new_investors_count: new_investors.length,
        conversion_percent: conversion_percent,
        aum: aum,
        avg_days_to_invest: avg_days_to_invest
      }
    end

    def monthly_invested_amount
      Share::BuyOrder.where(created_at: date_range, aasm_state: 'completed')
                      .sum(:quantity)
    end

    def new_investors
      User.joins(:buy_orders)
          .where(buy_orders: { created_at: date_range, aasm_state: 'completed' })
          .group('users.id')
          .having('MIN(buy_orders.created_at) >= ?', @start_date)
    end

    def conversion_percent
      sign_up_user_ids.length.positive? ? (new_investors.length / sign_up_user_ids.length.to_f) * 100.0 : "Can't determine"
    end

    def aum
      exit_order_property_ids = Share::Order.where(type: 'Share::ExitOrder')
                                            .pluck(:property_id)
                                            .uniq

      Property.visible.where.not(type: 'Property::Cause').where.not(id: exit_order_property_ids).sum do |property|
        ((property.share_count - property.available_shares) * property.share_price) / 100.0
      end
    end

    def avg_days_to_invest
      total_days = new_investors.sum do |user|
        completed_order = user.buy_orders.find_by(aasm_state: 'completed')

        ((completed_order.created_at - user.created_at) / 1.day).round(2).to_i
      end

      new_investors.length.positive? ? total_days / new_investors.length : "Can't determine"
    end

    private

    def sign_up_user_ids
      @sign_up_user_ids ||= User.where(created_at: date_range).ids
    end

    def date_range
      @start_date..@end_date
    end
  end
end
