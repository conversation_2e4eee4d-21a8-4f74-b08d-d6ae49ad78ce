require 'database_grants'

namespace :uown do
  namespace :db do
    desc 'Setup MySQL permissions for all environments'
    task setup_permissions: :environment do
      # adminservice
      DatabaseGrants.revoke_permission('adminservice', '*', [:select, :insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', '*', [:select])
      DatabaseGrants.grant_permission('adminservice', 'active_storage_attachments', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'active_storage_blobs', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'active_storage_variant_records', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'certification_answers', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'certification_levels', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'certification_questions', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'investment_documents', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'mandates', [:insert, :update])
      DatabaseGrants.grant_permission('adminservice', 'noticed_events', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'noticed_notifications', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'payment_logs', [:insert, :update])
      DatabaseGrants.grant_permission('adminservice', 'properties', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'property_certification_levels', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'property_dividends', [:insert, :update])
      DatabaseGrants.grant_permission('adminservice', 'property_documents', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'property_floorplans', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'property_legal_documents', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'property_news_items', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'property_photos', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'property_property_tags', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'property_payouts', [:insert, :update])
      DatabaseGrants.grant_permission('adminservice', 'property_payout_fees', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'property_tags', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'roles', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'share_logs', [:insert])
      DatabaseGrants.grant_permission('adminservice', 'share_orders', [:insert, :update])
      DatabaseGrants.grant_permission('adminservice', 'users', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'user_addresses', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'user_kyc_documents', [:insert, :update])
      DatabaseGrants.grant_permission('adminservice', 'user_login_attempts', [:insert])
      DatabaseGrants.grant_permission('adminservice', 'user_roles', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'user_queued_actions', [:insert, :update])
      DatabaseGrants.grant_permission('adminservice', 'user_states', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('adminservice', 'remote_bank_accounts', [:insert, :update, :delete])

      # apiservice
      DatabaseGrants.revoke_permission('apiservice', '*', [:select, :insert, :update, :delete])
      DatabaseGrants.grant_permission('apiservice', '*', [:select])
      DatabaseGrants.grant_permission('apiservice', 'mandates', [:insert, :update])
      DatabaseGrants.grant_permission('apiservice', 'noticed_events', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('apiservice', 'noticed_notifications', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('apiservice', 'payment_logs', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('apiservice', 'potential_investments', [:insert])
      DatabaseGrants.grant_permission('apiservice', 'potential_investment_items', [:insert, :update])
      DatabaseGrants.grant_permission('apiservice', 'share_orders', [:insert, :update])
      DatabaseGrants.grant_permission('apiservice', 'users', [:insert, :update])
      DatabaseGrants.grant_permission('apiservice', 'user_addresses', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('apiservice', 'user_certification_attempts', [:insert])
      DatabaseGrants.grant_permission('apiservice', 'user_kyc_documents', [:insert, :update])
      DatabaseGrants.grant_permission('apiservice', 'user_login_attempts', [:insert])
      DatabaseGrants.grant_permission('apiservice', 'user_queued_actions', [:insert, :update])
      DatabaseGrants.grant_permission('apiservice', 'user_states', [:insert])
      DatabaseGrants.grant_permission('apiservice', 'remote_bank_accounts', [:insert, :update, :delete])
      DatabaseGrants.grant_permission('apiservice', 'property_dividends', [:insert, :update])

    end

    desc 'Setup MySQL users for development environments'
    task setup_users: :environment do
      %w[adminservice apiservice].each do |username|
        password = SecureRandom.alphanumeric(20)

        puts "Creating mysql user #{username} with password #{password}"

        ActiveRecord::Migration.execute("CREATE USER '#{username}' IDENTIFIED BY '#{password}';")
      end
    end
  end
end
