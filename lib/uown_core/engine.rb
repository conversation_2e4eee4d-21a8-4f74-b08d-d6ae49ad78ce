require 'rails'
require 'uown_core'

# s.add_dependency in gemspec don't get automatically loaded
require 'aasm'
require 'active_storage_validations'
require 'after_commit_everywhere'
require 'aws-sdk-s3'
require 'bcrypt'
require 'countries'
require 'haml-rails'
require 'image_processing'
require 'mangopay'
require 'password_strength'
require 'sidekiq'
require 'uk_postcode'
require 'noticed'

module UownCore
  class Engine < ::Rails::Engine
    # Run engine migrations inside the main app
    initializer :append_migrations do |app|
      unless app.root.to_s.match root.to_s
        config.paths['db/migrate'].expanded.each do |expanded_path|
          app.config.paths['db/migrate'] << expanded_path
        end
      end
    end

    # Expose factories to parent project
    initializer 'model_core.factories', after: 'factory_bot.set_factory_paths' do
      FactoryBot.definition_file_paths << File.expand_path('../../../spec/factories', __FILE__) if defined?(FactoryBot)
    end

    # Configure generatora
    config.generators do |g|
      g.test_framework :rspec
    end
  end
end
