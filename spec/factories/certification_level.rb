FactoryBot.define do
  factory :certification_level, class: Certification::Level do
    sequence :name do |n|
      "certification-level-name-#{n}"
    end

    sequence :brief do |n|
      "certification-level-brief-#{n}"
    end

    sequence :description do |n|
      "certification-level-description-#{n}"
    end

    sequence :position do |n|
      n
    end

    attachment { Rack::Test::UploadedFile.new("#{UownCore::Engine.root}/spec/fixtures/files/png-transparent.png", 'image/png') }
    kind { Certification::Level::KINDS.sample }

    trait :default do
      default { true }
    end

    trait :with_questions do
      transient do
        question_count { 5 }
      end

      after(:create) do |certification_level, evaluator|
        create_list(:certification_question, evaluator.question_count, level: certification_level)
      end
    end
  end
end
