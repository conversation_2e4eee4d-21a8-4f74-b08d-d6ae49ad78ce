FactoryBot.define do
  factory :certification_question, class: Certification::Question do
    sequence :question do |n|
      "Question #{n}"
    end

    transient do
      answer_count { 5 }
    end

    after(:create) do |certification_question, evaluator|
      create_list(:certification_answer, evaluator.answer_count, question: certification_question)
    end

    # Associations
    level { create(:certification_level) }
  end
end
