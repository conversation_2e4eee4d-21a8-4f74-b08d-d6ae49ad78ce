FactoryBot.define do
  factory :potential_investment, class: PotentialInvestment do
    trait :with_items do
      transient do
        items_count { 1 }
      end

      after(:create) do |potential_investment, evaluator|
        create_list(:potential_investment_item, evaluator.items_count, potential_investment: potential_investment)
      end
    end

    # Associations
    user { create(:user_natural, :confirmed, :kyc_light_complete) }
  end
end
