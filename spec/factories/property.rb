FactoryBot.define do
  factory :property do
    sequence :name do |n|
      "Number #{n} - Somewhere Street"
    end

    spv_name { 'SPV Name' }
    description { 'Sample property description' }
    address_1 { '33 Stanmore Street' }
    address_2 { 'Cooper Close' }
    city { 'Leeds' }
    postcode { 'LS11 4AA' }

    share_count { 1_000 }
    funded { true }

    investment_case_and_risk { 'Sample investment case and risk' }

    trait :with_photos do
      transient do
        photos_count { 5 }
      end

      after(:create) do |property, evaluator|
        create_list(:property_photo, evaluator.photos_count, property: property)
      end
    end

    trait :with_floorplans do
      transient do
        floorplans_count { 5 }
      end

      after(:create) do |property, evaluator|
        create_list(:property_floorplan, evaluator.floorplans_count, property_id: property.id)
      end
    end

    trait :with_documents do
      transient do
        documents_count { 5 }
      end

      after(:create) do |property, evaluator|
        create_list(:property_document, evaluator.documents_count, property: property)
      end
    end

    trait :with_news_items do
      transient do
        news_items_count { 5 }
      end

      after(:create) do |property, evaluator|
        create_list(:property_news_item, evaluator.news_items_count, property: property)
      end
    end

    trait :with_tags do
      transient do
        tags_count { 5 }
      end

      after(:create) do |property, evaluator|
        create_list(:property_property_tag, evaluator.tags_count, property: property)
      end
    end
  end
end
