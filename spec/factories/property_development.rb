FactoryBot.define do
  factory :property_development, class: Property::Development, parent: :property do
    sequence :name do |n|
      "Development #{n} - Somewhere Street"
    end

    annualised_return { 2.0 }
    estimated_completion_date { Time.now + 5.years }

    # Currency Attributes
    gdv { 1_500_000 }
    finance { 250_000 }
    profit { 50_000 }
    property_amount { 1_000_000 }
    property_fee_legal_and_professional { 10 }
    site_value { 100_000 }

    trait :placeholder do
      placeholder { true }
    end
  end
end
