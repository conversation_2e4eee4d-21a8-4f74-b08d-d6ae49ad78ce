FactoryBot.define do
  factory :property_loan, class: Property::Loan, parent: :property do
    sequence :name do |n|
      "Loan #{n} - Somewhere Street"
    end

    annualised_return { 2.0 }
    estimated_completion_date { Time.now + 5.years }

    # Currency Attributes
    gdv { 1_500_000 }
    finance { 250_000 }
    profit { 50_000 }
    property_amount { 1_000_000 }
    property_fee_legal_and_professional { 10 }
    site_value { 100_000 }

    trait :unfunded do
      funded { false }
    end

    trait :placeholder do
      placeholder { true }
    end

    trait :with_acknowledgement_pdf do
      acknowledgement_pdf { Rack::Test::UploadedFile.new("#{UownCore::Engine.root}/spec/fixtures/files/document.pdf", 'application/pdf') }
    end
  end
end
