FactoryBot.define do
  factory :property_payout, class: Property::Payout do
    amount { rand(1..10_000) }

    # Associations
    property { create(:property_regular) }
    user { create(:user_natural) }

    # Traits
    trait :with_items do
      amount { 1000 }
      transient do
        item_count { 10 }
      end

      after(:create) do |property_payout_rent, evaluator|
        create_list(:property_payout_fee,
                    evaluator.item_count,
                    payout_id: property_payout_rent.id,
                    amount: 10)
      end
    end
  end
end
