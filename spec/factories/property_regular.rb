FactoryBot.define do
  factory :property_regular, class: Property::Regular, parent: :property do
    hpi { 2.0 }
    hpi_area { 'Leeds' }

    # Currency Attributes
    property_amount { 1_000_000 }
    rent_amount { 500 }
    property_fee_stamp_duty { 10 }
    property_fee_legal_and_professional { 10 }
    property_fee_pre_let_expenses { 10 }
    property_fee_repairs_provision { 10 }
    property_fee_deferred_tax { 10 }
    rental_fee_management { 10 }
    rental_fee_insurance { 10 }
    rental_fee_allowance_for_voids { 10 }
    rental_fee_maintenance_allowance { 10 }
    rental_fee_corporation_tax { 10 }
    rental_fee_deferred_fees { 10 }
    rental_fee_spv_charge { 10 }

    trait :unfunded do
      funded { false }
    end

    trait :placeholder do
      placeholder { true }
    end

    trait :easy_exit do
      funded { false }
      easy_exit { true }
    end
  end
end
