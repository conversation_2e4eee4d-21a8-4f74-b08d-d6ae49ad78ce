FactoryBot.define do
  factory :share_transfer_order, class: Share::TransferOrder do
    quantity { rand(1..10) }
    quantity_confirmation { quantity }
    description { 'Example description' }

    before(:create) do |object|
      create(:share_log, property: object.property,
                         user: object.source_user,
                         quantity: object.quantity)
    end

    # Associations
    creator { create(:user_natural, :confirmed, :kyc_light_complete) }
    property { create(:property_regular) }
    source_user { create(:user_natural, :confirmed, :kyc_light_complete) }
    user { create(:user_natural, :confirmed, :kyc_light_complete) }
  end
end
