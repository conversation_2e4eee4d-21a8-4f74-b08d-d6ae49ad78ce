FactoryBot.define do
  factory :user_address, class: User::Address do
    first_name { '<PERSON>' }
    last_name { 'Bloggs' }
    address_number { rand(1..50).to_s }
    address_1 { 'The Half Roundhouse' }
    city { 'Leeds' }
    country { 'GB' }
    post_code { 'LS12 1DR' }
    date_of_birth { 21.years.ago }

    # Associations
    user { create(:user_natural, :confirmed, :kyc_light_complete) }

    trait :personal do
      kind { 'personal' }
    end

  end
end
