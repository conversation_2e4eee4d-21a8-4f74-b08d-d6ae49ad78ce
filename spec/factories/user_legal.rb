FactoryBot.define do
  factory :user_legal, class: User::Legal do
    password { 'Passw0rd@1234' }
    authentication_token { SecureRandom.hex(8) }
    date_of_birth { 21.years.ago }

    sequence :email do |n|
      "userlegal-#{n}@dubitlimited.com"
    end

    trait :confirmed do
      confirmation_token { nil }
      confirmed_at { Time.now }
    end

    trait :kyc_light_complete do
      business_name { 'MyBusiness' }
      title { 'Mr' }
      first_name { 'Test' }
      last_name { 'User' }
      date_of_birth { 21.years.ago }
      phone_number { '0113 394 7920' }
      nationality { 'GB' }
      country_of_residence { 'GB' }
      legal_type { User::Legal::LEGAL_TYPES.sample }
      business_number { '123456' }

      after(:build) { |object| object.address = build(:user_address, kind: User::Address::PERSONAL, user: object) }
      after(:create) { |object| object.kyc_light_complete! }
    end

    trait :kyc_regular_submitted do
      after(:build) { |object| object.headquarters = build(:user_address, kind: User::Address::HEADQUARTERS, user: object) }

      transient do
        file { Base64.strict_encode64(File.open(File.join(UownCore::Engine.root,
                                                        'spec',
                                                        'fixtures',
                                                        'files',
                                                        'png-transparent.png'), 'rb').read) }
      end

      identity_proof { file }
      registration_proof { file }
      articles_of_association { file }
      shareholder_declaration { file }

      after(:create) { |object| object.kyc_regular_submitted! }
    end

    trait :kyc_regular_complete do
      after(:create) { |object| object.kyc_regular_complete! }
    end

    trait :kyc_regular_required do
      after(:create) { |object| object.kyc_regular_required! }
    end

    trait :kyc_regular_failed do
      after(:create) { |object| object.kyc_regular_failed! }
    end
  end
end
