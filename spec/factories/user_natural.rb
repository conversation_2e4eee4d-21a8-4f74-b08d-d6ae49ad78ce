FactoryBot.define do
  factory :user_natural, class: User::Natural do
    password { 'Passw0rd@1234' }
    authentication_token { SecureRandom.hex(8) }
    date_of_birth { 21.years.ago }

    sequence :email do |n|
      "user-#{n}@dubitlimited.com"
    end

    trait :confirmed do
      confirmation_token { nil }
      confirmed_at { Time.now }
    end

    trait :kyc_light_complete do
      title { 'Mr' }
      first_name { 'Test' }
      last_name { 'User' }
      call_me { true }
      date_of_birth { 21.years.ago }
      phone_number { '0113 394 7920' }
      nationality { 'GB' }
      country_of_residence { 'GB' }

      employment_status { 'Employed' }
      experience { 'Low' }
      planned_investment { '£0 - £500' }

      after(:build) { |object| object.address = build(:user_address, kind: User::Address::PERSONAL, user: object) }
      after(:create) { |object| object.kyc_light_complete! }
    end

    trait :kyc_regular_submitted do
      income_range { 1 }
      occupation { 'Programmer' }
      identity_proof { Base64.strict_encode64(File.open(File.join(UownCore::Engine.root,
                                                             'spec',
                                                             'fixtures',
                                                             'files',
                                                             'png-transparent.png'), 'rb').read) }
      after(:create) { |object| object.kyc_regular_submitted! }
    end

    trait :kyc_regular_complete do
      after(:create) { |object| object.kyc_regular_complete! }
    end

    trait :kyc_regular_required do
      after(:create) { |object| object.kyc_regular_required! }
    end

    trait :kyc_regular_failed do
      after(:create) { |object| object.kyc_regular_failed! }
    end
  end
end
