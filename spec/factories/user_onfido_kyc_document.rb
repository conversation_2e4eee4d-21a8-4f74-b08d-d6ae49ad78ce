FactoryBot.define do
  factory :user_onfido_kyc_document, class: User::OnfidoKycDocument, parent: :user_kyc_document do
    state { User::MangopayKycDocument::STATES.sample }

    trait :indentity do
      kind { 'identity' }
    end

    trait :document do
      kind { 'document' }
      base_64_image { 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg==' }
    end
  end
end
