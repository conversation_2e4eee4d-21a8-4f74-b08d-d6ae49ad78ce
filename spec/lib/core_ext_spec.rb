require 'rails_helper'

RSpec.describe 'core_ext' do
  describe '#titleize_with_respect' do
    it 'titleizes hyphened strings' do
      expect('hello-world'.titleize_with_respect).to eq('Hello-World')
    end

    it 'titleizes strings with apostrophes' do
      expect("o'hare".titleize_with_respect).to eq("O'Hare")
    end

    it 'titleizes strings with apostrophes and hyphens' do
      expect("o'hare-rabbit".titleize_with_respect).to eq("O'Hare-Rabbit")
    end
  end
end
