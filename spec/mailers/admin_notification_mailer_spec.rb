require "rails_helper"

RSpec.describe AdminNotificationMailer, type: :mailer do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:buy_order) { create(:share_buy_order) }
  let(:payment_log) { create(:payment_log) }
  let(:share_easy_exit_order) { create(:share_easy_exit_order) }
  let(:admin_email) { Rails.application.secrets.email[:admin_address] }

  before { stub_nuapay_create_account_service }
  describe 'signup' do
    it 'queues the email for delivery' do
      expect {
      described_class.signup(user).deliver_later(queue: 'default')
      }.to have_enqueued_job.on_queue('default')
    end

    it 'renders the subject' do
      mail = described_class.signup(user)
      expect(mail.subject).to eq('UOWN: User Signup')
    end

    it 'renders the receiver email' do
      mail = described_class.signup(user)
      expect(mail.to).to eq([admin_email])
    end
  end

  describe 'locked' do
    it 'queues the email for delivery' do
      expect {
        described_class.locked(user).deliver_later(queue: 'default')
      }.to have_enqueued_job.on_queue('default')
    end

    it 'renders the subject' do
      mail = described_class.locked(user)
      expect(mail.subject).to eq('UOWN: User Account Locked')
    end

    it 'renders the receiver email' do
      mail = described_class.locked(user)
      expect(mail.to).to eq([admin_email])
    end
  end

  describe 'shares_purchased' do
    it 'queues the email for delivery' do
      expect {
        described_class.shares_purchased(buy_order).deliver_later(queue: 'default')
      }.to have_enqueued_job.on_queue('default')
    end

    it 'renders the subject' do
      mail = described_class.shares_purchased(buy_order)
      expect(mail.subject).to eq('UOWN: Shares Purchased')
    end

    it 'renders the receiver email' do
      mail = described_class.shares_purchased(buy_order)
      expect(mail.to).to eq([admin_email])
    end
  end

  describe 'cause_contribution' do
    it 'queues the email for delivery' do
      expect {
        described_class.cause_contribution(buy_order).deliver_later(queue: 'default')
      }.to have_enqueued_job.on_queue('default')
    end

    it 'renders the subject' do
      mail = described_class.cause_contribution(buy_order)
      expect(mail.subject).to eq('UOWN: Cause Contribution')
    end

    it 'renders the receiver email' do
      mail = described_class.cause_contribution(buy_order)
      expect(mail.to).to eq([admin_email])
    end
  end

  describe 'payment_failure' do
    let(:mail) { described_class.payment_failure(payment_log).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('UOWN: Failed Payment')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([admin_email])
    end
  end

  describe 'easy_exit_order' do
    let(:property) { create(:property_regular) }
    let(:quantity) { 1 }
    let(:share_easy_exit_order) do
      create(:share_log, user: user,
                         property: property,
                         quantity: quantity)

      create(:share_easy_exit_order, user: user,
                                     property: property,
                                     quantity: quantity)
    end

    it 'queues the email for delivery' do
      expect {
        described_class.easy_exit_order(share_easy_exit_order).deliver_later(queue: 'default')
      }.to have_enqueued_job.on_queue('default')
    end

    it 'renders the subject' do
      mail = described_class.easy_exit_order(share_easy_exit_order)
      expect(mail.subject).to eq('UOWN: Easy Exit Order Created')
    end

    it 'renders the receiver email' do
      mail = described_class.easy_exit_order(share_easy_exit_order)
      expect(mail.to).to eq([admin_email])
    end

  end

  describe 'monthly_report' do
    let(:mail) { described_class.monthly_report(user).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('UOWN: Monthly Report')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end
end
