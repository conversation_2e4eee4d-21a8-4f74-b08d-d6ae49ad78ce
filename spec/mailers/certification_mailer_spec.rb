require 'rails_helper'

RSpec.describe CertificationMailer, type: :mailer do
  let(:user) { create(:user_natural) }

  describe '#first' do
    let(:mail) { described_class.first(user).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('UOWN Appropriateness Test Pointers')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end

  describe '#first_timeout' do
    let(:mail) { described_class.first_timeout(user).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Have another go at the test')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end

  describe '#second' do
    let(:mail) { described_class.second(user).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('UOWN Appropriateness Test Pointers')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end

  describe '#second_timeout' do
    let(:mail) { described_class.second_timeout(user).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Last chance to pass the test')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end

  describe '#third' do
    let(:mail) { described_class.third(user).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('UOWN Final Test Failed')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end
end
