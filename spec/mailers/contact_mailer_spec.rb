require 'rails_helper'

RSpec.describe Contact<PERSON>ail<PERSON>, type: :mailer do
  let(:user) { create(:user_natural) }
  let(:email_body) { 'General email body containing text.' }
  let(:email_subject) { 'General subject for email' }
  let(:admin_email) { Rails.application.secrets.email[:admin_address] }
  let(:from_email) { Rails.application.secrets.email[:from_address] }

  describe 'successful' do
    let(:mail) { described_class.contact_us(user, email_body, email_subject).deliver_now }

    it 'renders the receiver email' do
      expect(mail.to).to eq([admin_email])
      expect(mail.reply_to).to eq([user.email])
      expect(mail.from).to eq([from_email])
      expect(mail.body.encoded).to include(email_body)
    end
  end
end
