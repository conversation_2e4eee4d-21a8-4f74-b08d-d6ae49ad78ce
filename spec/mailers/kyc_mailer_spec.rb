require 'rails_helper'

RSpec.describe KYCMail<PERSON>, type: :mailer do
  let(:user) { create(:user_natural) }

  describe 'successful' do
    let(:mail) { described_class.successful(user).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Your identity check was successful')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end

  describe '#failure' do
    let(:mail) { described_class.failure(user, 'other').deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Your identity check has failed')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end

    it 'assigns @kyc_document' do
      expect(mail.text_part.body.encoded).to include('We have had some trouble processing your ID document. Please contact us by replying to this email for further information.')
    end
  end
end
