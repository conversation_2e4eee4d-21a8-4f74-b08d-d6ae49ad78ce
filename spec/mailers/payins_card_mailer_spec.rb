require 'rails_helper'

RSpec.describe PayinsCardMailer, type: :mailer do
  let(:user) { create(:user_natural) }
  let(:payin) do
    {
      'CreditedFunds' => { 'Amount' => 2000 },
      'Fees' => { 'Amount' => 2000 }
    }
  end

  describe 'successful' do
    let(:mail) { described_class.successful(user, payin).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('You succesfully funded your account')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end
end
