require "rails_helper"

RSpec.describe PayoutsBankWireMailer, type: :mailer do
  let(:user) { create(:user_natural) }
  let(:payment_log) { create(:payment_log, user: user) }

  describe 'successful' do
    let(:mail) { described_class.successful(user, payment_log).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Your payout bank transfer was successful')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end

  describe '#failure' do
    let(:mail) { described_class.failure(user, payment_log).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Your payout bank transfer has failed')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end
end
