require "rails_helper"

RSpec.describe PropertyDividendMailer, type: :mailer do
  before { stub_nuapay_create_account_service }
  describe 'rent' do
    let(:property_payout_rent) { create(:property_payout_rent) }
    let(:property_dividend) { create(:property_dividend, amount: 599, payout: property_payout_rent) }
    let(:amount_in_pounds) { property_dividend.amount_to_pay.to_d / 100 }
    let(:mail) { described_class.rent(property_dividend).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq("You received £#{amount_in_pounds} dividend for #{property_dividend.property.name}")
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([property_dividend.user.email])
    end
  end

  describe 'interest' do
    let(:property) { create(:property_loan) }
    let(:property_payout_rent) { create(:property_payout_rent, property: property) }
    let(:property_dividend) { create(:property_dividend, amount: 599, payout: property_payout_rent) }
    let(:amount_in_pounds) { property_dividend.amount_to_pay.to_d / 100 }
    let(:mail) { described_class.interest(property_dividend).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq("You received £#{amount_in_pounds} in income for #{property_dividend.property.name}")
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([property_dividend.user.email])
    end
  end

  describe 'development' do
    let(:property) { create(:property_development) }
    let(:property_payout_property) { create(:property_payout_property, property: property) }
    let(:property_dividend) { create(:property_dividend, amount: 599, payout: property_payout_property, property: property) }
    let(:mail) { described_class.development(property_dividend).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq("💷 You've been paid! 💷")
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([property_dividend.user.email])
    end
  end

  describe 'loan' do
    let(:property) { create(:property_loan) }
    let(:property_payout_property) { create(:property_payout_property, property: property) }
    let(:property_dividend) { create(:property_dividend, amount: 599, payout: property_payout_property, property: property) }
    let(:mail) { described_class.development(property_dividend).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq("💷 You've been paid! 💷")
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([property_dividend.user.email])
    end
  end

  describe 'property' do
    let(:property) { create(:property_regular) }
    let(:property_payout_property) { create(:property_payout_property, property: property) }
    let(:property_dividend) { create(:property_dividend, amount: 599, payout: property_payout_property, property: property) }

    let!(:exit_order) do
      create(:share_log, property: property, user: property_dividend.user, quantity: 5)
      create(:share_exit_order, property: property, user: property_dividend.user, quantity: 5, payout: property_payout_property)
    end

    let(:mail) { described_class.property(property_dividend).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq("💷 You've been paid! 💷")
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([property_dividend.user.email])
    end
  end
end
