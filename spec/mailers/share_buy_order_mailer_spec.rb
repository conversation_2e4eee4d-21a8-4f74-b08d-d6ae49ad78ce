require 'rails_helper'

RSpec.describe ShareBuyOrder<PERSON>ailer, type: :mailer do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:property) { create(:property_regular) }
  let(:quantity) { 1 }
  let(:share_buy_order) { create(:share_buy_order, quantity: quantity, user: user, property: property) }

  before { stub_nuapay_create_account_service }
  describe 'bought' do
    let(:mail) { described_class.bought(share_buy_order).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq("Purchased #{share_buy_order.quantity} shares in #{share_buy_order.property.name}")
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end

    it 'assigns @link' do
      expect(mail.text_part.body.encoded).to include("#{Rails.application.secrets.frontend_url}/investments/#{share_buy_order.id}/receipt.pdf")
    end
  end

  describe 'loan' do
    let(:property) { create(:property_loan, :unfunded, :with_acknowledgement_pdf) }
    let(:mail) { described_class.loan(share_buy_order).deliver_now }
    let(:amount_in_pounds) { share_buy_order.total_amount.to_d / 100 }

    it 'renders the subject' do
      expect(mail.subject).to eq("You have just invested £#{sprintf("%0.02f",amount_in_pounds)} in #{share_buy_order.property.name}")
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end

    it 'has the acknowledgement_pdf attached' do
      expect(mail.attachments.map(&:filename)).to include('acknowledgement.pdf')
    end
  end

  describe 'failed' do
    let(:reason) { '' }
    let(:mail) { described_class.failed(user, reason).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Unfortunately we were unable to place your investment')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end

    context 'with different reasons' do
      context 'Insufficient funds' do
        let(:reason) { 'Insufficient funds' }

        it 'renders the body' do
          expected_text = 'Unfortunately placing your investment failed because your account has insufficient funds.'

          expect(mail.text_part.to_s).to include(expected_text)
        end
      end

      context 'Invalid share order/s' do
        let(:reason) { 'Invalid share order/s' }

        it 'renders the body' do
          expected_text = 'Unfortunately placing your investment failed as one or more shares are no longer available to purchase.'

          expect(mail.text_part.to_s).to include(expected_text)
        end
      end

      context 'Bankwire failed' do
        let(:reason) { 'Bankwire failed' }

        it 'renders the body' do
          expected_text = 'Unfortunately placing your investment failed because the bankwire was not received.'

          expect(mail.text_part.to_s).to include(expected_text)
        end
      end
    end
  end
end
