require "rails_helper"

RSpec.describe ShareEasyExitOrderMailer, type: :mailer do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:property) { create(:property_regular) }
  let(:quantity) { 1 }
  let(:share_easy_exit_order) do
    create(:share_log, user: user,
                       property: property,
                       quantity: quantity)

    create(:share_easy_exit_order, user: user,
                                   property: property,
                                   quantity: quantity)
  end
  before { stub_nuapay_create_account_service }
  describe 'created' do
    let(:mail) { described_class.created(share_easy_exit_order).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Your easy exit request has been created')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([share_easy_exit_order.user.email])
    end
  end

  describe 'completed' do
    let(:mail) { described_class.completed(share_easy_exit_order).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq("Sold #{share_easy_exit_order.quantity} shares in #{share_easy_exit_order.property.name}")
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([share_easy_exit_order.user.email])
    end
  end

  describe 'rejected' do
    let(:mail) { described_class.rejected(share_easy_exit_order).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Your easy exit request has been cancelled')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([share_easy_exit_order.user.email])
    end
  end
end
