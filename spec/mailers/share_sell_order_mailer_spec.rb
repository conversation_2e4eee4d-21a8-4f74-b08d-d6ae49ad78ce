require "rails_helper"

RSpec.describe ShareSellOrderMailer, type: :mailer do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:property) { create(:property_regular) }
  let(:quantity) { 1 }
  let(:share_sell_order) do
    create(:share_log, user: user,
                       property: property,
                       quantity: quantity)

    create(:share_sell_order, user: user,
                              property: property,
                              quantity: quantity)
  end
  before { stub_nuapay_create_account_service }
  describe 'successful' do
    let(:mail) { described_class.sold(share_sell_order, quantity).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq("Sold #{quantity} shares in #{share_sell_order.property.name}")
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end
end
