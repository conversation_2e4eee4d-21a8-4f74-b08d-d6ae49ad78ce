require 'rails_helper'

RSpec.describe ShareTransferOrderMailer, type: :mailer do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:property) { create(:property_regular) }
  let(:quantity) { 1 }
  let(:share_transfer_order) { create(:share_transfer_order, quantity: quantity, user: user, property: property) }
  before { stub_nuapay_create_account_service }
  describe 'successful' do
    let(:mail) { described_class.complete(share_transfer_order).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq("Received #{share_transfer_order.quantity} shares in #{share_transfer_order.property.name}")
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end
  end
end
