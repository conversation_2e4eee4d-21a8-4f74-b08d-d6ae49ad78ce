require "rails_helper"

RSpec.describe UserLockedMailer, type: :mailer do
  let(:user) { create(:user_natural) }

  describe 'locked' do
    let(:mail) { described_class.locked(user, Time.now.utc).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('We have temporarily locked your UOWN account')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end

    it 'assigns @link' do
      expect(mail.text_part.body.encoded).to include("#{Rails.application.secrets.frontend_url}/password_reset")
    end
  end
end
