require 'rails_helper'

RSpec.describe UserPasswordMailer, type: :mailer do
  let(:user) { create(:user_natural) }

  describe 'reset' do
    let(:mail) { described_class.reset(user).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Password reset instructions')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end

    it 'assigns @link' do
      user.ensure_reset_token!

      expect(mail.text_part.body.encoded).to include("#{Rails.application.secrets.frontend_url}/users/passwords/edit?id=#{user.id}&amp;reset_token=#{user.reset_token}")
    end
  end
end
