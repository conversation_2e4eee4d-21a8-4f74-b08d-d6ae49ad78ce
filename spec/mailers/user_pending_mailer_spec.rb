require 'rails_helper'

RSpec.describe UserPendingMailer, type: :mailer do
  let(:user) { create(:user_natural) }

  describe 'reset' do
    let(:mail) { described_class.registration(user).deliver_now }

    it 'renders the subject' do
      expect(mail.subject).to eq('Confirm your UOWN account')
    end

    it 'renders the receiver email' do
      expect(mail.to).to eq([user.email])
    end

    it 'assigns @link' do
      user.ensure_reset_token!

      expect(mail.text_part.body.encoded).to include("#{Rails.application.secrets.frontend_url}/users/confirmations/confirm?id=#{user.id}&amp;confirmation_token=#{user.confirmation_token}")
    end
  end
end
