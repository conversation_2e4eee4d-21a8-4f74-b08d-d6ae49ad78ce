RSpec.configure do |config|
  config.before(:each) do
    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/test/users/12345/bankaccounts')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_bank_accounts.json").read)

    stub_request(:get, "https://api.sandbox.mangopay.com/v2.01/test/users/12345/bankaccounts/1")
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_bank_account.json").read)
  end
end
