RSpec.configure do |config|
  config.before(:each) do
    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/test/users/12345/KYC/documents/12345')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_kyc.json").read)

    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/users/12345/KYC/documents')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_kyc.json").read)

    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/users/54321/KYC/documents')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_kyc.json").read)

    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/users/12345/KYC/documents/12345/pages')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_kyc.json").read)

    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/users/54321/KYC/documents/12345/pages')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_kyc.json").read)

    stub_request(:put, 'https://api.sandbox.mangopay.com/v2.01/test/users/12345/KYC/documents/12345/pages')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_kyc.json").read)

    stub_request(:put, 'https://api.sandbox.mangopay.com/v2.01/test/users/54321/KYC/documents/12345/pages')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_kyc.json").read)

    stub_request(:put, 'https://api.sandbox.mangopay.com/v2.01/test/users/12345/KYC/documents/12345')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_kyc.json").read)

    stub_request(:put, 'https://api.sandbox.mangopay.com/v2.01/test/users/54321/KYC/documents/12345')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_kyc.json").read)
  end
end
