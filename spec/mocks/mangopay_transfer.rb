RSpec.configure do |config|
  config.before(:each) do
    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/transfers')
      .with(body: /{"Tag":"(\d*)-(\d*)-(\d*)","AuthorId":"12345","CreditedUserId":"12345","DebitedFunds":{"Currency":"GBP","Amount":(\d*)},"Fees":{"Currency":"GBP","Amount":(\d*)},"DebitedWalletId":"1","CreditedWalletId":"1"}/)
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_transfer.json").read)

    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/transfers')
      .with(body: "{\"Tag\":\"1-12345-2\",\"AuthorId\":\"12345\",\"CreditedUserId\":\"12345\",\"DebitedFunds\":{\"Currency\":\"GBP\",\"Amount\":5000},\"Fees\":{\"Currency\":\"GBP\",\"Amount\":300},\"DebitedWalletId\":\"1\",\"CreditedWalletId\":\"1\"}")
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_transfer.json").read)

    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/transfers')
      .with(body: "{\"Tag\":\"1-2-2\",\"AuthorId\":\"12345\",\"CreditedUserId\":\"12345\",\"DebitedFunds\":{\"Currency\":\"GBP\",\"Amount\":1020},\"Fees\":{\"Currency\":\"GBP\",\"Amount\":20},\"DebitedWalletId\":\"1\",\"CreditedWalletId\":\"1\"}")
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_bad_transfer.json").read)

    stub_request(:post, "https://api.sandbox.mangopay.com/v2.01/test/transfers")
      .with(body: /{\"Tag\":\"payout-(\d*)\",\"AuthorId\":\"(\d*)\",\"CreditedUserId\":\"(\d*)\",\"DebitedFunds\":{\"Currency\":\"GBP\",\"Amount\":(\d*)},\"Fees\":{\"Currency\":\"GBP\",\"Amount\":(\d*)},\"DebitedWalletId\":\"(\d*)\",\"CreditedWalletId\":\"(\d*)\"}/)
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_transfer.json").read)

    stub_request(:post, "https://api.sandbox.mangopay.com/v2.01/test/transfers")
      .with(body: /{\"Tag\":\"dividend-(\d*)-(\d*).(\d*)\",\"AuthorId\":\"(\d*)\",\"CreditedUserId\":\"(\d*)\",\"DebitedFunds\":{\"Currency\":\"GBP\",\"Amount\":(\d*)},\"Fees\":{\"Currency\":\"GBP\",\"Amount\":(\d*)},\"DebitedWalletId\":\"(\d*)\",\"CreditedWalletId\":\"(\d*)\"}/)
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_transfer.json").read)

    stub_request(:post, "https://api.sandbox.mangopay.com/v2.01/test/transfers")
      .with(body: /{\"Tag\":\"payout-fees-(\d*)\",\"AuthorId\":\"(\d*)\",\"CreditedUserId\":\"(\d*)\",\"DebitedFunds\":{\"Currency\":\"GBP\",\"Amount\":(\d*)},\"Fees\":{\"Currency\":\"GBP\",\"Amount\":(\d*)},\"DebitedWalletId\":\"(\d*)\",\"CreditedWalletId\":\"(\d*)\"}/)
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_transfer.json").read)
  end
end
