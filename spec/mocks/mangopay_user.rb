RSpec.configure do |config|
  config.before(:each) do
    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/users/natural')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_user.json").read)

    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/users/legal')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_user_legal.json").read)

    stub_request(:put, 'https://api.sandbox.mangopay.com/v2.01/test/users/natural/12345')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_user.json").read)

    stub_request(:put, 'https://api.sandbox.mangopay.com/v2.01/test/users/legal/54321')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_user_legal.json").read)
  end
end
