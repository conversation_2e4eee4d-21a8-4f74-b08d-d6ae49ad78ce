require 'rails_helper'

RSpec.describe Certification::Level, type: :model do
  it { is_expected.to have_many(:questions) }
  it { is_expected.to have_many(:users) }

  it { is_expected.to have_many(:property_certification_levels) }
  it { is_expected.to have_many(:properties).through(:property_certification_levels) }

  it { is_expected.to validate_presence_of(:name) }
  it { is_expected.to validate_presence_of(:kind) }
  it { is_expected.to validate_inclusion_of(:kind).in_array(described_class::KINDS) }

  it { is_expected.to validate_attached_of(:attachment) }
  it { is_expected.to validate_content_type_of(:attachment).allowing('image/jpeg', 'image/gif', 'image/png') }

  it { should accept_nested_attributes_for(:questions) }

  describe '.set_default' do
    let(:certification_level) { create(:certification_level, :default, kind: 'natural') }
    let(:certification_level2) { create(:certification_level, :default, kind: 'natural') }

    it 'should set any other records to false on save' do
      certification_level

      expect(certification_level.default).to eq(true)

      certification_level2

      expect(certification_level2.default).to eq(true)
      expect(certification_level.reload.default).to eq(false)
    end
  end
end
