# Module: models

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
* `{Method1}`: {description}
* `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
* [Task1]: {brief description}
* [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
* [Task1]: {brief description}
* [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---

---KEY_DEFINITIONS_START---
Key Definitions:
2G#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models
2G1#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/investment_document_spec.rb
2G2#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/mandate_spec.rb
2G3#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/payment_log_spec.rb
2G4#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/potential_investment_item_spec.rb
2G5#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/potential_investment_spec.rb
2G6#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/property_certification_level_spec.rb
2G7#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/property_spec.rb
2G8#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/remote_bank_account_spec.rb
2G9#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/user_lockable_spec.rb
2G10#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/user_spec.rb
2Ga#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/certification
2Gb#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/property
2Gc#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/share
2Gd#2: /Volumes/NVMe_Drive/Documents/Website/ruby-platform/uown-core/spec/models/user
---KEY_DEFINITIONS_END---

last_KEY_edit: Assigned keys: 2G, 2G1, 2G1, 2G2, 2G2, 2G3, 2G3, 2G4, 2G4, 2G5, 2G5, 2G6, 2G6, 2G7, 2G7, 2G8, 2G8, 2G9, 2G9, 2G10, 2G10, 2Ga, 2Gb, 2Gc, 2Gd
last_GRID_edit: Grid structure updated (2025-06-10T09:18:08.400196)

---GRID_START---
X 2G#2 2G1#2 2G2#2 2G3#2 2G4#2 2G5#2 2G6#2 2G7#2 2G8#2 2G9#2 2G10#2 2Ga#2 2Gb#2 2Gc#2 2Gd#2
2G#2 = ox14
2G1#2 = xop13
2G2#2 = xpop12
2G3#2 = xppop11
2G4#2 = xp3op10
2G5#2 = xp4op9
2G6#2 = xp5op8
2G7#2 = xp6op7
2G8#2 = xp7op6
2G9#2 = xp8op5
2G10#2 = xp9op4
2Ga#2 = xp10op3
2Gb#2 = xp11opp
2Gc#2 = xp12op
2Gd#2 = xp13o
---GRID_END---

---mini_tracker_end---
