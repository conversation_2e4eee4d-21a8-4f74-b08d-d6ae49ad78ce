require 'rails_helper'

RSpec.describe PaymentLog, type: :model do
  it { is_expected.to belong_to(:dividend) }
  it { is_expected.to belong_to(:potential_investment) }
  it { is_expected.to belong_to(:user) }

  it { is_expected.to validate_presence_of(:user) }
  it { is_expected.to validate_presence_of(:remote_id) }
  it { is_expected.to validate_presence_of(:kind) }
  it { is_expected.to validate_presence_of(:status) }

  let(:payment_log) { create(:payment_log) }
  let(:potential_investment) { create(:potential_investment, :with_items) }
  let(:total_amount) { potential_investment.share_orders.sum(&:total_with_fees) }
  let(:status) { 'success' }
  before { stub_nuapay_create_account_service }
  describe '#successful!' do
    context 'with potential investment' do
      let!(:payment_log) { create(:payment_log, potential_investment: potential_investment) }

      context 'with valid pi' do
        it 'has a queued action' do
          expect(payment_log.queued_action).not_to be(nil)
        end

        it 'has a queued action with the same potential investment as itself' do
          expect(payment_log.queued_action.target).to eq(potential_investment)
        end

        it 'processes the queued action and marks it as complete' do
          expect{ payment_log.successful!(status) }.to change{ Share::BuyOrder.count }.by(potential_investment.items.size)
          expect(payment_log.queued_action.aasm_state).to eq('completed')
        end
      end
    end
  end

  describe '#failure!' do
    let(:status) { 'failed' }

    it 'sets status and failed_at' do
      payment_log.failure!(status)

      expect(payment_log.status).to eq(status)
      expect(payment_log.failed_at).not_to eq(nil)
    end

    it 'sends an admin notification' do
      # Init the payment log so the user gets created
      payment_log

      expect { payment_log.failure!(status) }.to have_enqueued_job.on_queue('mailers')
    end
  end
end
