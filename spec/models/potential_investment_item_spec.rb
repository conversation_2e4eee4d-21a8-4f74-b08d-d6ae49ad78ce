require 'rails_helper'

RSpec.describe PotentialInvestmentItem, type: :model do
  it { is_expected.to belong_to(:potential_investment) }

  it { is_expected.to validate_presence_of(:potential_investment) }
  it { is_expected.to validate_presence_of(:property) }
  it { is_expected.to validate_presence_of(:quantity) }

  let(:quantity) { 5 }
  let(:property) { create(:property_regular) }
  let(:potential_investment) { create(:potential_investment) }
  let(:potential_investment_item) { build(:potential_investment_item, potential_investment: potential_investment, property: property, quantity: quantity) }
  before { stub_nuapay_create_account_service }
  describe '.buy_order' do
    it 'should correctly stub out buy order' do
      expect(potential_investment_item.buy_order.quantity).to eq(potential_investment_item.quantity)
      expect(potential_investment_item.buy_order.property).to eq(potential_investment_item.property)
    end
  end

  describe '.validate_buy_order' do
    let(:available_shares) { property.available_shares }
    let(:quantity) { available_shares + 1 }

    it 'should return the number of items' do
      expect(potential_investment_item.buy_order.quantity).to eq(quantity)
      expect(potential_investment_item.valid?).to eq(false)
      expect(potential_investment_item.errors[:quantity]).to eq(["you can't purchase more shares than what are available (#{available_shares})"])
    end
  end
end
