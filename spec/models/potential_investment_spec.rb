require 'rails_helper'

RSpec.describe PotentialInvestment, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to belong_to(:user) }

  it { is_expected.to have_many(:items) }
  it { is_expected.to have_many(:payment_logs) }

  it { should accept_nested_attributes_for(:items) }

  it { is_expected.to validate_presence_of(:user) }

  let(:potential_investment) { create(:potential_investment, :with_items) }
  let(:total_amount) { potential_investment.share_orders.sum(&:total_with_fees) }

  describe '.share_orders' do
    it 'should return the number of items' do
      expect(potential_investment.share_orders.size).to eq(potential_investment.items.size)
    end
  end

  describe '.invest!' do
    context 'all valid' do
      it 'creates valid buy orders' do
        expect(potential_investment.items.size).to eq(1)
        expect{ potential_investment.invest! }.to change{ Share::BuyOrder.count }.by(1)
      end
    end

    context 'a share order is invalid' do
      before(:each) do
        allow_any_instance_of(Share::BuyOrder).to receive(:valid?).and_return(false)
      end

      it 'raises an error if the orders are invalid' do
        expect { potential_investment.invest! }.to raise_error('Invalid share order/s')
      end
    end
  end
end
