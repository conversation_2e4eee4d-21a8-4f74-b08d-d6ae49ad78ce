require 'rails_helper'

RSpec.describe Property::Cause, type: :model do
  it { is_expected.to validate_presence_of(:property_amount) }

  it { is_expected.to validate_numericality_of(:property_amount) }

  let(:property) { create(:property_cause, property_amount: 1500, share_count: 1000) }
  before { stub_nuapay_create_account_service }
  describe '#target_amount' do
    it 'is calculated correctly' do
      expected_amount = property.property_amount

      expect(property.target_amount).to eq(expected_amount)
    end
  end

  describe '#unique_contributors' do
    let(:share_log_1) { create(:share_log, property: property) }
    let(:share_log_2) { create(:share_log, property: property, user: share_log_1.user) }
    let(:share_log_3) { create(:share_log, property: property) }

    it 'does not include the root user' do
      expect(property.share_logs.count).to eq(1)
      expect(property.unique_contributors).to eq(0)
    end

    it 'doesnt count the same person twice' do
      share_log_1
      share_log_2
      share_log_3

      expect(property.share_logs.count).to eq(4)
      expect(property.unique_contributors).to eq(2)
    end
  end
end
