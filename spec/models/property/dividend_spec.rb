require 'rails_helper'

RSpec.describe Property::Dividend, type: :model do
  before do
    stub_nuapay_create_account_service
    old_dividend.payment_logs.destroy_all
  end
  let(:property_payout_rent) { create(:property_payout_rent) }
  let(:property_dividend) { create(:property_dividend, payout: property_payout_rent) }
  let(:outstanding_amount) { 200 }

  let(:old_rent_payment) do
    create(:property_payout_rent, property: property_dividend.payout.property,
                                  start_date: property_dividend.payout.start_date - 1.year,
                                  end_date: property_dividend.payout.end_date - 1.year)
  end

  let!(:old_dividend) do
    create(:property_dividend, :completed, user: property_dividend.user,
                                           payout: old_rent_payment,
                                           amount: outstanding_amount)
  end

  it { is_expected.to validate_presence_of(:payout) }
  it { is_expected.to validate_presence_of(:property) }
  it { is_expected.to validate_presence_of(:user) }

  it { is_expected.to belong_to(:property) }
  it { is_expected.to belong_to(:payout) }
  it { is_expected.to belong_to(:user) }
  it { is_expected.to have_many(:payment_logs) }

  describe '#amount_to_pay' do
    it 'is outstanding amount plus amount floored' do
      expected_amount = (property_dividend.amount + outstanding_amount).floor

      expect(property_dividend.amount_to_pay).to eq(expected_amount)
    end
  end

  describe '#outstanding_amount' do
    it 'includes any outstanding amount' do
      expect(property_dividend.outstanding_amount_as_of_payout_date(false)).to eq(outstanding_amount)
    end
  end

  describe '#generate_idempotency_key' do
    it 'isnt blank on a new record' do
      expect(property_dividend.idempotency_key).not_to be(nil)
    end
  end
end
