require 'rails_helper'

RSpec.describe Property::Loan, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to validate_presence_of(:annualised_return) }
  it { is_expected.to validate_presence_of(:estimated_completion_date) }

  it { is_expected.to validate_presence_of(:finance) }
  it { is_expected.to validate_presence_of(:gdv) }
  it { is_expected.to validate_presence_of(:profit) }
  it { is_expected.to validate_presence_of(:property_amount) }
  it { is_expected.to validate_presence_of(:property_fee_legal_and_professional) }
  it { is_expected.to validate_presence_of(:site_value) }
  it { is_expected.to validate_presence_of(:spv_name) }

  it { is_expected.to validate_presence_of(:address_1) }
  it { is_expected.to validate_presence_of(:address_2) }
  it { is_expected.to validate_presence_of(:city) }
  it { is_expected.to validate_presence_of(:postcode) }

  it { is_expected.to validate_numericality_of(:finance) }
  it { is_expected.to validate_numericality_of(:gdv) }
  it { is_expected.to validate_numericality_of(:profit) }
  it { is_expected.to validate_numericality_of(:property_amount) }
  it { is_expected.to validate_numericality_of(:property_fee_legal_and_professional) }
  it { is_expected.to validate_numericality_of(:site_value) }
  it { is_expected.to validate_numericality_of(:term) }

  it { is_expected.to validate_content_type_of(:acknowledgement_pdf).allowing('application/pdf') }

  let(:property) { create(:property_loan, property_amount: 1500, share_count: 1000, funded: false) }

  describe '#target_amount' do
    it 'is calculated correctly' do
      expected_amount = property.property_amount +
                        property.property_fee_legal_and_professional +
                        property.site_value -
                        property.finance

      expect(property.target_amount).to eq(expected_amount)
    end
  end

  describe '#available_shares' do
    context 'unfunded' do
      it 'must function as normal' do
        expect(property.funded).to eq(false)
        expect(property.available_shares).to eq(property.share_count)
      end
    end

    context 'funded' do
      it 'must always be zero' do
        expect(property.available_shares).to eq(property.share_count)
        property.update!(funded: true)
        expect(property.reload.funded).to eq(true)
        expect(property.reload.available_shares).to eq(0)
      end
    end
  end
end
