require 'rails_helper'

RSpec.describe Property::NewsItem, type: :model do
  it { is_expected.to belong_to(:property) }
  it { is_expected.to belong_to(:user) }

  it { is_expected.to validate_presence_of(:content) }
  it { is_expected.to validate_presence_of(:date) }
  it { is_expected.to validate_presence_of(:property) }
  it { is_expected.to validate_presence_of(:title) }
  it { is_expected.to validate_presence_of(:user) }

  describe '#set_defaults' do
    it 'sets the date field to today' do
      new_news_item = Property::NewsItem.new

      expect(new_news_item.date).to eq(Time.zone.today)
    end
  end
end
