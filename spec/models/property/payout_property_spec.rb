require 'rails_helper'

RSpec.describe Property::PayoutProperty, type: :model do
  before { stub_nuapay_create_account_service }
  let(:quantity) { 5 }
  let(:property) { create(:property_development, created_at: 1.month.ago, funded: true) }
  let(:payout_property) { create(:property_payout_property, amount: 100, property: property) }
  let(:kyc_complete_light_user) { create(:user_natural, :confirmed, :kyc_light_complete) }

  ## shared

  it_behaves_like 'it acts like a property payout' do
    let(:payout) { payout_property }
  end

  ## validation

  describe '#check_property_type' do
    let(:payout_property) { build(:property_payout_property, property: property) }

    it 'is valid for a development' do
      expect(payout_property.valid?).to eq(true)
    end

    it 'is valid for a loan' do
      payout_property.property = create(:property_loan, funded: true)

      expect(payout_property.valid?).to eq(true)
    end

    it 'is valid for a property regular' do
      payout_property.property = create(:property_regular, funded: true)

      expect(payout_property.valid?).to eq(true)
    end

    it 'is invalid for a property cause' do
      payout_property.property = create(:property_cause, funded: true)

      expect(payout_property.valid?).to eq(false)
      expect(payout_property.errors[:property_id]).to eq(["can't be a cause"])
    end

    it 'is invalid for a non funded' do
      payout_property.property = create(:property_development, funded: false)

      expect(payout_property.valid?).to eq(false)
      expect(payout_property.errors[:property_id]).to eq(['must be marked as funded'])
    end

    it 'is invalid if the property has active sell orders' do
      create(:share_log, property: payout_property.property, user: kyc_complete_light_user, quantity: 1)
      create(:share_sell_order, property: payout_property.property, user: kyc_complete_light_user, quantity: 1)

      expect(payout_property.valid?).to eq(false)
      expect(payout_property.errors[:property_id]).to eq(['has 1 active sell orders, please cancel them first'])
    end
  end

  ## after create

  describe '#create_exit_orders' do
    let(:build_share_logs) { create_list(:share_log, 5, property: property, quantity: quantity) }
    let(:build_root_user_shares) { create(:share_log, property: property, quantity: quantity, user: User.admin_uown) }

    it 'builds an exit order for each item in grouped_dividends' do
      build_share_logs
      build_root_user_shares

      expect(property.share_logs.pluck(:user_id).uniq.size).to eq(quantity + 1)
      expect(payout_property.exit_orders.size).to be > 0
      expect(payout_property.exit_orders.size).to eq(quantity)
    end

    it 'does not create a share log for someone who has sold all their shares' do
      build_share_logs.each do |sl|
        create(:share_log, property: property, quantity: -quantity, user: sl.user)
      end

      expect(payout_property.exit_orders.size).to eq(0)
    end
  end

  ## calculations

  describe '#grouped_dividends' do
    it 'returns the correct hash' do
      expected_result = payout_property.send(:grouped_shares_on).collect { |sl| { user: sl.user, amount: payout_property.total_per_share * sl.total } }

      expect(payout_property.send(:grouped_dividends).size).to be > 0
      expect(payout_property.send(:grouped_dividends)).to eq(expected_result)
    end
  end
end
