require 'rails_helper'

RSpec.describe Property::PayoutRent, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to validate_presence_of(:start_date) }
  it { is_expected.to validate_presence_of(:end_date) }

  let(:quantity) { 5 }
  let(:property) { create(:property_regular, created_at: 1.month.ago) }
  let(:start_date) { property.created_at.to_date }
  let(:end_date) { Date.today }
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }

  let(:build_payout_rent) { build(:property_payout_rent, amount: 100, start_date: start_date, end_date: end_date, property: property) }
  let(:new_payout_rent) { create(:property_payout_rent, amount: 100, start_date: start_date, end_date: end_date, property: property) }

  let(:build_share_logs) { create_list(:share_log, quantity, property: property, quantity: 1) }

  let(:build_share_logs_for_one_user) do
    create(:share_log, property: property, quantity: 1, user: user, created_at: (new_payout_rent.date_range).to_a.sample)
  end

  let(:build_old_share_logs_for_one_user) do
    create_list(:share_log, quantity, property: property, quantity: 1, created_at: 2.weeks.ago, user: user)
  end

  ## shared

  it_behaves_like 'it acts like a property payout' do
    let(:payout) { new_payout_rent }
  end

  ## validation

  describe '#start_date' do
    let(:old_rent_payment) { create(:property_payout_rent, start_date: 1.month.ago, end_date: 1.week.ago) }

    it 'is not in the future' do
      build_payout_rent.start_date = Date.today + 1.day
      build_payout_rent.valid?

      expect(build_payout_rent.errors[:start_date]).to include('must be before today')
    end

    it 'cannot equal the end date' do
      build_payout_rent.end_date = build_payout_rent.start_date
      build_payout_rent.valid?

      expect(build_payout_rent.errors[:start_date]).to include('cannot be the same as the end date')
    end

    it 'shouldnt clash with other rent payments' do
      build_payout_rent.property = old_rent_payment.property
      build_payout_rent.start_date = 2.weeks.ago
      build_payout_rent.valid?

      expect(build_payout_rent.errors.inspect).to include('clashes with another rent payment')
    end

    it 'cant be inside another rent payment' do
      build_payout_rent.property = old_rent_payment.property
      build_payout_rent.start_date = old_rent_payment.start_date + 1.day
      build_payout_rent.end_date = old_rent_payment.end_date - 1.day
      build_payout_rent.valid?

      expect(build_payout_rent.errors.inspect).to include('clashes with another rent payment')
    end

    it 'cant be outside another rent payment' do
      build_payout_rent.property = old_rent_payment.property
      build_payout_rent.start_date = old_rent_payment.start_date - 1.day
      build_payout_rent.end_date = old_rent_payment.end_date + 1.day
      build_payout_rent.valid?

      expect(build_payout_rent.errors.inspect).to include('clashes with another rent payment')
    end
  end

  describe '#end_date' do
    it 'is after the start_date' do
      build_payout_rent.end_date = new_payout_rent.start_date - 1.day
      build_payout_rent.valid?

      expect(build_payout_rent.errors[:end_date]).to include('must be after the start date')
    end
  end

  describe '#calculated_dividends' do
    it 'returns an array of users with totals' do
      build_share_logs

      expect(new_payout_rent.calculated_dividends.size).to be > 0

      date_range = new_payout_rent.start_date..new_payout_rent.end_date
      total_per_share_per_day = new_payout_rent.total_per_share / date_range.to_a.size

      date_range.each do |date|
        new_payout_rent.send(:grouped_shares_on, date).each do |share_log|
          expect(new_payout_rent.calculated_dividends).to include(
            user: share_log.user,
            quantity: share_log.total,
            amount: total_per_share_per_day * share_log.total,
            date: date
          )
        end
      end
    end
  end

  describe '#grouped_dividends' do
    it 'is group .dividends' do
      build_share_logs_for_one_user

      unique_user_ids = new_payout_rent.calculated_dividends.collect { |d| d[:user].id }.uniq.size

      expect(new_payout_rent.calculated_dividends.size).to be > new_payout_rent.grouped_dividends.size
      expect(new_payout_rent.grouped_dividends.size).to eq(unique_user_ids)
    end
  end

  ## helpers

  describe '#date_range' do
    it 'is between the start and end dates' do
      expect(new_payout_rent.date_range).to eq(start_date..end_date)
    end
  end
end
