require 'rails_helper'

RSpec.describe Property::Payout, type: :model do
  it { is_expected.to belong_to(:property) }
  it { is_expected.to belong_to(:user) }

  it { is_expected.to have_many(:dividends) }
  it { is_expected.to have_many(:fees) }
  it { is_expected.to have_many(:exit_orders) }

  it { is_expected.to validate_presence_of(:amount) }
  it { is_expected.to validate_presence_of(:property) }
  it { is_expected.to validate_presence_of(:user) }
end
