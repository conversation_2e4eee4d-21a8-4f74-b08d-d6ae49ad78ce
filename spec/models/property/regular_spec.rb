require 'rails_helper'

RSpec.describe Property::Regular, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to validate_presence_of(:hpi) }
  it { is_expected.to validate_presence_of(:hpi_area) }

  it { is_expected.to validate_presence_of(:property_amount) }
  it { is_expected.to validate_presence_of(:rent_amount) }
  it { is_expected.to validate_presence_of(:property_fee_stamp_duty) }
  it { is_expected.to validate_presence_of(:property_fee_legal_and_professional) }
  it { is_expected.to validate_presence_of(:property_fee_pre_let_expenses) }
  it { is_expected.to validate_presence_of(:property_fee_repairs_provision) }
  it { is_expected.to validate_presence_of(:property_fee_deferred_tax) }
  it { is_expected.to validate_presence_of(:rental_fee_management) }
  it { is_expected.to validate_presence_of(:rental_fee_insurance) }
  it { is_expected.to validate_presence_of(:rental_fee_allowance_for_voids) }
  it { is_expected.to validate_presence_of(:rental_fee_maintenance_allowance) }
  it { is_expected.to validate_presence_of(:rental_fee_corporation_tax) }
  it { is_expected.to validate_presence_of(:rental_fee_deferred_fees) }
  it { is_expected.to validate_presence_of(:rental_fee_spv_charge) }
  it { is_expected.to validate_presence_of(:spv_name) }

  it { is_expected.to validate_presence_of(:address_1) }
  it { is_expected.to validate_presence_of(:address_2) }
  it { is_expected.to validate_presence_of(:city) }
  it { is_expected.to validate_presence_of(:postcode) }

  it { is_expected.to validate_numericality_of(:property_amount) }
  it { is_expected.to validate_numericality_of(:rent_amount) }
  it { is_expected.to validate_numericality_of(:property_fee_stamp_duty) }
  it { is_expected.to validate_numericality_of(:property_fee_legal_and_professional) }
  it { is_expected.to validate_numericality_of(:property_fee_pre_let_expenses) }
  it { is_expected.to validate_numericality_of(:property_fee_repairs_provision) }
  it { is_expected.to validate_numericality_of(:property_fee_deferred_tax) }
  it { is_expected.to validate_numericality_of(:rental_fee_management) }
  it { is_expected.to validate_numericality_of(:rental_fee_insurance) }
  it { is_expected.to validate_numericality_of(:rental_fee_allowance_for_voids) }
  it { is_expected.to validate_numericality_of(:rental_fee_maintenance_allowance) }
  it { is_expected.to validate_numericality_of(:rental_fee_corporation_tax) }
  it { is_expected.to validate_numericality_of(:rental_fee_deferred_fees) }
  it { is_expected.to validate_numericality_of(:rental_fee_spv_charge) }

  let(:property) { create(:property_regular, property_amount: 1500, share_count: 1000) }

  describe '#target_amount' do
    it 'is calculated correctly' do
      expected_amount = property.property_amount +
                        property.property_fee_stamp_duty +
                        property.property_fee_legal_and_professional +
                        property.property_fee_pre_let_expenses +
                        property.property_fee_repairs_provision -
                        property.property_fee_deferred_tax

      expect(property.target_amount).to eq(expected_amount)
    end
  end

  describe '#check_property_mode' do
    let(:property) { build(:property_regular, funded: true, easy_exit: true) }
    let(:message) { 'Property cannot be funded and instant exit' }

    it 'should prevent funded and easy exit' do
      expect(property.valid?).to eq(false)
      expect(property.errors[:funded]).to eq([message])
      expect(property.errors[:easy_exit]).to eq([message])
    end
  end
end
