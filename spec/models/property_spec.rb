require 'rails_helper'

RSpec.describe Property, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to have_many(:documents) }
  it { is_expected.to have_many(:floorplans) }
  it { is_expected.to have_many(:legal_documents) }
  it { is_expected.to have_many(:news_items) }
  it { is_expected.to have_many(:photos) }
  it { is_expected.to have_many(:buy_orders) }
  it { is_expected.to have_many(:sell_orders) }
  it { is_expected.to have_many(:potential_investment_items) }
  it { is_expected.to have_many(:property_tags) }
  it { is_expected.to have_many(:tags) }

  it { is_expected.to have_many(:property_certification_levels) }
  it { is_expected.to have_many(:certification_levels).through(:property_certification_levels) }

  it { is_expected.to validate_presence_of(:name) }
  it { is_expected.to validate_presence_of(:share_count) }

  let(:property) { create(:property, property_amount: 1500, share_count: 1000) }

  describe '#create_wallet_and_sell_order' do
    context 'standard property' do
      it 'has sell orders and shares' do
        expect(property.sell_orders.count).to eq(1)
        expect(property.available_shares).to eq(property.share_count)
      end
    end

    context 'placeholder regular property' do
      let(:placeholder_property) { create(:property_regular, :placeholder) }
      it 'doesnt have sell orders or shares' do
        expect(placeholder_property.sell_orders.count).to eq(0)
        expect(placeholder_property.available_shares).to eq(0)
        expect(placeholder_property.available_shares).not_to eq(property.share_count)
      end
    end

    context 'placeholder development property' do
      let(:placeholder_property) { create(:property_development, :placeholder) }
      it 'doesnt have sell orders or shares' do
        expect(placeholder_property.sell_orders.count).to eq(0)
        expect(placeholder_property.available_shares).to eq(0)
        expect(placeholder_property.available_shares).not_to eq(property.share_count)
      end
    end
  end

  describe '#share_price' do
    it 'should allow fractions of a pence' do
      expect(property.share_price).to eq(1.5)
    end
  end

  describe '#generate_slug' do
    it 'should generate the slug correctly' do
      expect(property.slug).to eq(property.name.parameterize)
    end
  end

  describe '#upgrade_from_placeholder' do
    let!(:placeholder_property) { create(:property_regular, :placeholder) }

    context 'when placeholder' do
      it 'has placeholder set to true' do
        expect(placeholder_property.placeholder).to be(true)
      end

      it 'has funded set to true' do
        expect(placeholder_property.funded).to be(true)
      end

      it 'has zero shares available' do
        expect(placeholder_property.available_shares).to eq(0)
      end

      it 'has no sell orders' do
      expect(placeholder_property.sell_orders.count).to eq(0)
      end
    end

    context 'when upgraded' do
      before do
        placeholder_property.upgrade_from_placeholder!
        placeholder_property.reload
      end

      it 'has placeholder set to false' do
        expect(placeholder_property.placeholder).to be(false)
      end

      it 'has funded set to false' do
        expect(placeholder_property.funded).to be(false)
      end

      it 'has shares available' do
        expect(placeholder_property.available_shares).to eq(property.share_count)
      end

      it 'has a sell order' do
        expect(placeholder_property.sell_orders.count).to eq(1)
      end
    end
  end
end
