# spec/models/remote_bank_account_spec.rb
require 'rails_helper'

RSpec.describe RemoteBankAccount, type: :model do
  # Test associations
  describe 'associations' do
    it { should belong_to(:bankable) }
  end

  # Test valid_bank? method
  describe '#valid_bank?' do
    let(:remote_bank_account) { build(:remote_bank_account) }

    context 'when account has account_number and status is ACTIVE' do
      it 'returns true' do
        remote_bank_account.account_number = '********'
        remote_bank_account.account_number = '********'
        remote_bank_account.iban = nil
        remote_bank_account.status = 'ACTIVE'

        expect(remote_bank_account.valid_bank?).to be true
      end
    end

    context 'when account has iban and status is ACTIVE' do
      it 'returns true' do
        remote_bank_account.account_number = nil
        remote_bank_account.iban = '**********************'
        remote_bank_account.status = 'ACTIVE'

        expect(remote_bank_account.valid_bank?).to be true
      end
    end

    context 'when account has both account_number and iban with status ACTIVE' do
      it 'returns true' do
        remote_bank_account.account_number = '********'
        remote_bank_account.iban = '**********************'
        remote_bank_account.status = 'ACTIVE'

        expect(remote_bank_account.valid_bank?).to be true
      end
    end

    context 'when account has account_number but status is not ACTIVE' do
      it 'returns false' do
        remote_bank_account.account_number = '********'
        remote_bank_account.iban = nil
        remote_bank_account.status = 'INACTIVE'

        expect(remote_bank_account.valid_bank?).to be false
      end
    end

    context 'when account has iban but status is not ACTIVE' do
      it 'returns false' do
        remote_bank_account.account_number = nil
        remote_bank_account.iban = '**********************'
        remote_bank_account.status = 'INACTIVE'

        expect(remote_bank_account.valid_bank?).to be false
      end
    end


    context 'when iban is empty string' do
      it 'returns false' do
        remote_bank_account.account_number = nil
        remote_bank_account.iban = ''
        remote_bank_account.status = 'ACTIVE'

        expect(remote_bank_account.valid_bank?).to be false
      end
    end

    context 'when status is nil' do
      it 'returns false' do
        remote_bank_account.account_number = '********'
        remote_bank_account.iban = nil
        remote_bank_account.status = nil

        expect(remote_bank_account.valid_bank?).to be false
      end
    end
  end

  # Test invalid_bank? method
  describe '#invalid_bank?' do
    let(:remote_bank_account) { build(:remote_bank_account) }

    context 'when valid_bank? returns true' do
      it 'returns false' do
        remote_bank_account.account_number = '********'
        remote_bank_account.status = 'ACTIVE'

        expect(remote_bank_account.invalid_bank?).to be false
      end
    end
  end

  # Test polymorphic association scenarios
  describe 'polymorphic association' do
    before { stub_nuapay_create_account_service }
    let(:user) { create(:user_natural) }
    let(:property) { create(:property) }

    context 'when associated with a User' do
      it 'can be created with user as bankable' do
        bank_account = create(:remote_bank_account, bankable: user)

        expect(bank_account.bankable).to eq(user)
        expect(bank_account.bankable_type).to eq('User')
      end
    end

    context 'when associated with a Company' do
      it 'can be created with company as bankable' do
        bank_account = create(:remote_bank_account, bankable: property)

        expect(bank_account.bankable_id).to eq(property.id)
        expect(bank_account.bankable_type).to eq('Property')
      end
    end
  end
end