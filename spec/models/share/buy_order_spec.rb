require 'rails_helper'

RSpec.describe Share::BuyOrder, type: :model do
  before { stub_nuapay_create_account_service }
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:user_without_funds) { create(:user_natural) }
  let(:property) { create(:property_regular, :unfunded) }
  let(:funded_property) { create(:property_regular) }
  let(:new_buy_order)    { build(:share_buy_order, quantity: 1, property: property, user: user) }
  let(:create_buy_order) { create(:share_buy_order, quantity: 1, property: property, user: user) }
  let(:create_share_log) { create(:share_log, quantity: 1, property: property, user: user) }

  describe '.check_and_transfer_funds' do
    it 'should set the wallet_id to the temporary wallet id' do
      new_buy_order.send(:set_total_amount)
      new_buy_order.save

      expect(new_buy_order.aasm_state).to eq('funds_allocated')
    end
  end

  describe '.calculated_fees' do
    it 'should calculate the fees and return an integer' do
      new_buy_order.send(:set_total_amount)

      expected_amount = (new_buy_order.total_amount * Share::BuyOrder::FEE_PERCENTAGE).to_i

      expect(new_buy_order.calculated_fees).to eq(expected_amount)
    end

    it 'should add stamp duty if the property is funded' do
      new_buy_order.property = funded_property
      new_buy_order.send(:set_total_amount)

      percentage = Share::BuyOrder::FEE_PERCENTAGE + Share::BuyOrder::STAMP_DUTY_PERCENTAGE

      expected_amount = (new_buy_order.total_amount * percentage).to_i

      expect(new_buy_order.calculated_fees).to eq(expected_amount)
    end
  end

  describe '.total_with_fees' do
    it 'should be the total plus any fees' do
      new_buy_order.send(:set_total_amount)

      expected_amount = new_buy_order.total_amount + new_buy_order.calculated_fees

      expect(new_buy_order.total_with_fees).to eq(expected_amount)
    end
  end

  describe '.validate_shares_available' do
    let(:quantity) { new_buy_order.property.available_shares + 1 }

    it 'adds error when more than available' do
      new_buy_order.quantity = quantity

      expect(new_buy_order.valid?).to eq(false)
      expect(new_buy_order.errors[:quantity].first).to eq("you can't purchase more shares than what are available (#{new_buy_order.property.available_shares})")
    end
  end

  describe '.send_receipt' do
    context 'when property/development' do
      before(:each) do
        create_buy_order
        ActionMailer::Base.deliveries = []
      end

      it 'sends a receipt to the user and the admin' do
        expected_subject = I18n.t('share_buy_order_mailer.bought.subject', property: property.name, quantity: create_buy_order.quantity)

        expect { create_buy_order.send_receipt }.to have_enqueued_job.on_queue('mailers').exactly(:twice)
      end
    end

    context 'when cause' do
      let(:cause) { create(:property_cause) }
      let(:create_cause_buy_order) { create(:share_buy_order, quantity: 1, property: cause, user: user) }

      before(:each) do
        create_cause_buy_order
        ActionMailer::Base.deliveries = []
      end

      it 'sends a receipt to the user and the admin' do
        expected_subject = I18n.t('share_buy_order_mailer.contributed.subject', property: cause.name)

        expect { create_cause_buy_order.send_receipt }.to have_enqueued_job.on_queue('mailers').exactly(:twice)
      end
    end
  end
end
