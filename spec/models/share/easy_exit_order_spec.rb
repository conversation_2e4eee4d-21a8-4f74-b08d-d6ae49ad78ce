require 'rails_helper'

RSpec.describe Share::EasyExitOrder, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to validate_presence_of(:property) }
  it { is_expected.to validate_presence_of(:quantity) }
  it { is_expected.to validate_presence_of(:user) }

  it { is_expected.to validate_numericality_of(:quantity) }

  it { is_expected.to have_many(:order_transactions) }
  it { is_expected.to belong_to(:property) }
  it { is_expected.to belong_to(:user) }

  let!(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let!(:root_user) { User.admin_uown }
  let(:valid_property) { create(:property_regular) }
  let(:quantity) { 10 }
  let(:share_easy_exit_order) do
    create(:share_log, user: user,
                       property: valid_property,
                       quantity: quantity)

    create(:share_easy_exit_order, user: user,
                                   property: valid_property,
                                   quantity: quantity)
  end

  it_behaves_like 'it acts like a sell order' do
    let(:invalid_property) { create(:property_regular, :unfunded) }
    let(:share_order) { Share::EasyExitOrder.new(quantity: 1) }
    let(:add_shares) { Share::Log.new(user: user, property: invalid_property, quantity: 1000).save(validate: false) }
  end

  describe '#calculated_fees' do
    it 'should calculate the fees and return an integer' do
      share_easy_exit_order.send(:set_total_amount)

      expected_amount = (share_easy_exit_order.total_before_fees * Share::EasyExitOrder::FEE_PERCENTAGE).to_i

      expect(share_easy_exit_order.calculated_fees).to eq(expected_amount)
    end
  end

  describe '#total_with_fees' do
    it 'should be the total minus any fees' do
      share_easy_exit_order.send(:set_total_amount)

      expected_amount = share_easy_exit_order.total_before_fees - share_easy_exit_order.calculated_fees

      expect(share_easy_exit_order.total_with_fees).to eq(expected_amount)
    end
  end

  describe '#send_notifications' do
    it 'sends an notifications' do
      expect { share_easy_exit_order }.to have_enqueued_job.on_queue('mailers').exactly(:twice)
    end
  end
end
