require 'rails_helper'

RSpec.describe Share::ExitOrder, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to validate_presence_of(:property) }
  it { is_expected.to validate_presence_of(:quantity) }
  it { is_expected.to validate_presence_of(:user) }

  it { is_expected.to validate_numericality_of(:quantity) }

  it { is_expected.to have_many(:order_transactions) }
  it { is_expected.to belong_to(:property) }
  it { is_expected.to belong_to(:user) }

  let!(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:property) { create(:property_development) }
  let(:quantity) { 10 }
  let(:share_exit_order) do
    create(:share_log, user: user,
                       property: property,
                       quantity: quantity)

    create(:share_exit_order, user: user,
                              property: property,
                              quantity: quantity)
  end

  describe '#total_before_fees' do
    it 'is identical to total amount' do
      share_exit_order.send(:set_total_amount)

      expect(share_exit_order.total_before_fees).to eq(share_exit_order.total_amount)
    end
  end

  describe '#total_with_fees' do
    it 'is identical to total amount' do
      share_exit_order.send(:set_total_amount)

      expect(share_exit_order.total_with_fees).to eq(share_exit_order.total_amount)
    end
  end
end
