require 'rails_helper'

RSpec.describe Share::Log, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to validate_presence_of(:user) }
  it { is_expected.to validate_presence_of(:property) }
  it { is_expected.to validate_presence_of(:quantity) }
  it { is_expected.to validate_presence_of(:buy_order) }
  it { is_expected.to validate_presence_of(:sell_order) }
  it { is_expected.to validate_numericality_of(:quantity) }

  it { is_expected.to belong_to(:user) }
  it { is_expected.to belong_to(:property) }
  it { is_expected.to belong_to(:buy_order) }
  it { is_expected.to belong_to(:sell_order) }

  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:property) { create(:property_regular) }
  let(:quantity) { 1 }
  let(:share_log) { create(:share_log, quantity: quantity, user: user, property: property) }
  let(:negative_share_log) { create(:share_log, quantity: -quantity, user: user, property: property) }

  describe '.set_total_amount' do
    it 'should get set on creation' do
      expected_amount = (share_log.quantity * share_log.property.share_price).floor

      expect(share_log.total_amount).to eq(expected_amount)
    end

    it 'should be negative when the quantity is negative' do
      expected_amount = (negative_share_log.quantity * negative_share_log.property.share_price).floor

      expect(negative_share_log.total_amount).to eq(expected_amount)
      expect(negative_share_log.total_amount).to be < 0
    end
  end
end
