require 'rails_helper'

RSpec.describe Share::Order, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to validate_presence_of(:property) }
  it { is_expected.to validate_presence_of(:quantity) }
  it { is_expected.to validate_presence_of(:user) }

  it { is_expected.to validate_numericality_of(:quantity).is_greater_than(0).only_integer }

  it { is_expected.to have_many(:order_transactions) }
  it { is_expected.to belong_to(:property) }
  it { is_expected.to belong_to(:payout).optional }
  it { is_expected.to belong_to(:user) }

  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:property) { create(:property_regular) }
  let(:quantity) { 1 }
  let(:share_buy_order) { create(:share_buy_order, quantity: quantity, user: user, property: property) }

  describe '.quantity_remaining' do
    it 'should be calculated correctly' do
      expected_quantity = share_buy_order.quantity - share_buy_order.order_transactions.sum(:quantity)

      expect(share_buy_order.quantity_remaining).to eq(expected_quantity)
    end

    it 'should throw exception if quantity_remaining is less than zero' do
      # Create transaction to force remainig shares negative
      create(:share_order_transaction, order: share_buy_order, quantity: share_buy_order.quantity_remaining + 1)

      expect { share_buy_order.quantity_remaining }.to raise_exception(RuntimeError, 'Quantity remaining is less than zero')
    end

    it 'should return zero if quantity is nil on a fresh unsaved record' do
      # This was previously causing errors in serialisation for a
      # new record where .quantity was nil. Now handle in a graceful way
      new_share_order = Share::Order.new
      expect(new_share_order.quantity_remaining).to eq(0)
    end
  end

  describe '.set_total_amount' do
    context 'whole share amount' do
      it 'should get set on creation' do
        expected_amount = (share_buy_order.quantity * share_buy_order.property.share_price).floor

        expect(share_buy_order.total_amount).to eq(expected_amount)
      end
    end

    context 'decimal share price' do
      let(:property) { create(:property, property_amount: 1500, share_count: 1000) }

      it 'should be rounded down to the nearest pence' do
        expect(share_buy_order.property.share_price).to eq(1.5)

        expected_amount = (share_buy_order.quantity * share_buy_order.property.share_price).floor

        expect(share_buy_order.total_amount).to eq(expected_amount)
      end
    end
  end

  describe '.quantity_available_to_sell' do
    let(:new_sell_order) { Share::SellOrder.new(quantity: 1, property: property, user: user) }
    let(:new_easy_exit_order) { Share::EasyExitOrder.new(quantity: 1, property: property, user: user) }
    let!(:add_shares) { Share::Log.new(user: user, property: property, quantity: 1000).save(validate: false) }
    let(:root_user) { User.admin_uown }
    let(:total_logs) { Share::Log.where(user: user, property: property).sum(:quantity) }

    it 'should take into account any share logs' do
      expect(new_sell_order.quantity_available_to_sell).to eq(total_logs)
    end

    it 'should take into account any active sell orders' do
      new_sell_order.quantity = 10
      new_sell_order.save(validate: false)

      so = Share::SellOrder.new(quantity: 1, user: user, property: property)

      expect(so.quantity_available_to_sell).to eq(total_logs - 10)
    end

    it 'should take into account any active easy exit orders' do
      new_easy_exit_order.quantity = 10
      new_easy_exit_order.save(validate: false)

      so = Share::SellOrder.new(quantity: 1, user: user, property: property)

      expect(so.quantity_available_to_sell).to eq(total_logs - 10)
    end

    it 'check sell order quantity remaining is used rather than quantity' do
      new_sell_order.quantity = 10
      new_sell_order.save(validate: false)

      Share::OrderTransaction.create!(order: new_sell_order, quantity: 1, user: user)

      so = Share::SellOrder.new(quantity: 1, user: user, property: property)

      expect(new_sell_order.quantity).not_to eq(new_sell_order.quantity_remaining)
      expect(so.quantity_available_to_sell).not_to eq(total_logs - new_sell_order.quantity)
      expect(so.quantity_available_to_sell).to eq(total_logs - new_sell_order.quantity_remaining)
    end
  end
end
