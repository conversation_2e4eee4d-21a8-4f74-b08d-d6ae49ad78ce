require 'rails_helper'

RSpec.describe Share::SellOrder, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to validate_presence_of(:property) }
  it { is_expected.to validate_presence_of(:quantity) }
  it { is_expected.to validate_presence_of(:user) }

  it { is_expected.to validate_numericality_of(:quantity) }

  it { is_expected.to have_many(:order_transactions) }
  it { is_expected.to belong_to(:property) }
  it { is_expected.to belong_to(:user) }

  it_behaves_like 'it acts like a sell order' do
    let!(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
    let!(:root_user) { User.admin_uown }
    let(:valid_property) { create(:property_regular) }
    let(:invalid_property) { create(:property_regular, :unfunded) }
    let(:share_order) { Share::SellOrder.new(quantity: 1) }
    let(:add_shares) { Share::Log.new(user: user, property: invalid_property, quantity: 1000).save(validate: false) }
  end
end
