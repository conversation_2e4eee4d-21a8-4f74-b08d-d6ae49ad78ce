require 'rails_helper'

RSpec.describe Share::TransferOrder, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to validate_presence_of(:property) }
  it { is_expected.to validate_presence_of(:quantity) }
  it { is_expected.to validate_presence_of(:user) }
  it { is_expected.to validate_presence_of(:source_user) }
  it { is_expected.to validate_presence_of(:creator) }
  it { is_expected.to validate_presence_of(:description) }

  it { is_expected.to validate_numericality_of(:quantity) }

  it { is_expected.to have_many(:order_transactions) }
  it { is_expected.to belong_to(:property) }
  it { is_expected.to belong_to(:user) }
  it { is_expected.to belong_to(:source_user) }
  it { is_expected.to belong_to(:creator) }

  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:property) { create(:property_regular) }
  let(:new_record) { build(:share_transfer_order, property: property, source_user: user) }

  describe '.check_quantity_confirmation' do
    let!(:create_share_log) { create(:share_log, property: property, user: user, quantity: new_record.quantity) }

    it 'doesnt error when they match' do
      expect(new_record.valid?).to eq(true)
      expect(new_record.quantity).to eq(new_record.quantity_confirmation)
    end

    it 'errors when they dont match' do
      new_record.quantity_confirmation = -1

      expect(new_record.valid?).to eq(false)
      expect(new_record.quantity).not_to eq(new_record.quantity_confirmation)
      expect(new_record.errors[:quantity_confirmation].first).to eq('doesnt match quantity')
    end
  end

  describe '.check_quantity_confirmation' do
    let(:create_share_log) { create(:share_log, property: property, user: user, quantity: new_record.quantity) }

    it 'doesnt error when the user has enough shares' do
      create_share_log

      expect(user.shares_available_for_property(property)).to be >= new_record.quantity
      expect(new_record.valid?).to eq(true)
    end

    it 'errors when the user doesnt have enough shares' do
      expect(new_record.valid?).to eq(false)
      expect(user.shares_available_for_property(property)).to be < new_record.quantity
      expect(new_record.errors[:source_user_id].first).to eq('only has 0 shares available')
    end
  end
end
