require 'rails_helper'

RSpec.describe User::Address, type: :model do
  it { is_expected.to belong_to(:user) }
  it { is_expected.to validate_presence_of(:address_1) }
  it { is_expected.to validate_presence_of(:city) }
  it { is_expected.to validate_presence_of(:country) }
  it { is_expected.to validate_presence_of(:post_code) }
  it { is_expected.to validate_inclusion_of(:kind).in_array(described_class::KINDS) }

  context 'directors' do
    subject { create(:user_address, kind: User::Address::DIRECTOR) }

    it { is_expected.to validate_presence_of(:date_of_birth) }
    it { is_expected.to validate_presence_of(:first_name) }
    it { is_expected.to validate_presence_of(:last_name) }
  end

  context 'shareholders' do
    subject { create(:user_address, kind: User::Address::SHAREHOLDER) }

    it { is_expected.to validate_presence_of(:date_of_birth) }
    it { is_expected.to validate_presence_of(:first_name) }
    it { is_expected.to validate_presence_of(:last_name) }
  end

  it 'titleizes the address' do
    address = create(:user_address, address_1: '1 house', city: 'salt-lake', post_code: 'ls12 1dr', kind: User::Address::DIRECTOR)

    expect(address.address_1).to eq('1 House')
    expect(address.city).to eq('Salt-Lake')
    expect(address.post_code).to eq('LS12 1DR')
  end

  describe '#check_postcode_format' do
    let(:address) { create(:user_address, kind: User::Address::PERSONAL) }

    it 'accepts valid postcodes' do
      address.country = 'GB'
      address.post_code = 'LS12 1DR'

      expect(address.valid?).to eq(true)
    end

    it 'adds a validate error for invalid postcodes' do
      address.country = 'GB'
      address.post_code = '90210'

      expect(address.valid?).to eq(false)
      expect(address.errors[:post_code]).to eq(['is invalid'])
    end

    it 'ignores non UK postcodes' do
      address.country = 'US'
      address.post_code = '90210'

      expect(address.valid?).to eq(true)
    end
  end
end
