require 'rails_helper'

RSpec.describe User::CertificationAttempt, type: :model do
  it { is_expected.to belong_to(:user) }
  it { is_expected.to belong_to(:certification_level) }
  it { is_expected.to validate_presence_of(:user) }
  it { is_expected.to validate_presence_of(:certification_level) }
  it { is_expected.to validate_presence_of(:state) }
  it { is_expected.to validate_inclusion_of(:state).in_array(described_class::STATES) }
end
