require 'rails_helper'

RSpec.describe User::Legal, type: :model do
  let(:user) { create(:user_legal, :confirmed, :kyc_light_complete) }
  let(:kyc_regular_user) { create(:user_legal, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }
  let(:property) { create(:property_regular) }


  describe 'factories' do
    let(:one) { create(:user_legal) }
    let(:two) { create(:user_legal, :confirmed) }
    let(:three) { create(:user_legal, :confirmed, :kyc_light_complete) }
    let(:four) { create(:user_legal, :confirmed, :kyc_light_complete) }
    let(:five) { create(:user_legal, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }

    it 'is valid' do
      expect(one.valid?).to eq(true)
      expect(two.valid?).to eq(true)
      expect(three.valid?).to eq(true)
      expect(four.valid?).to eq(true)
      expect(five.valid?).to eq(true)
    end
  end

  describe 'kyc_light_user validation' do
    subject { User::Legal.new(aasm_state: 'kyc_light_is_complete') }

    it { is_expected.to validate_presence_of(:business_name) }
    it { is_expected.to validate_presence_of(:first_name) }
    it { is_expected.to validate_presence_of(:last_name) }
    it { is_expected.to validate_presence_of(:country_of_residence) }
    it { is_expected.to validate_presence_of(:date_of_birth) }
    it { is_expected.to validate_presence_of(:nationality) }
    it { is_expected.to validate_presence_of(:address) }
    it { is_expected.to validate_presence_of(:title) }
    it { is_expected.to validate_presence_of(:legal_type) }
  end

  describe 'kyc_regular_user validation' do
    subject { User::Legal.new(aasm_state: 'kyc_regular_is_pending') }

    it { is_expected.to validate_presence_of(:headquarters) }
    it { is_expected.to validate_presence_of(:identity_proof) }
    it { is_expected.to validate_presence_of(:registration_proof) }

    context 'business' do
      subject { User::Legal.new(aasm_state: 'kyc_regular_is_pending', legal_type: 'BUSINESS') }

      it { is_expected.to validate_presence_of(:articles_of_association) }
      it { is_expected.to validate_presence_of(:shareholder_declaration) }
    end
  end

  describe '.valid_kyc_documents?' do
    it 'returns false when document doesnt exist' do
      user.kyc_documents.destroy_all

      expect(user.valid_kyc_documents?).to eq(false)
    end

    context 'business' do
      # it 'returns true when document exists' do
      #   user.legal_type = 'BUSINESS'
      #
      #   user.mangopay_kyc_documents.create(kind: 'identity_proof', state: 'VALIDATED')
      #   user.mangopay_kyc_documents.create(kind: 'registration_proof', state: 'VALIDATED')
      #   user.mangopay_kyc_documents.create(kind: 'articles_of_association', state: 'VALIDATED')
      #   user.mangopay_kyc_documents.create(kind: 'shareholder_declaration', state: 'VALIDATED')
      #
      #   expect(user.valid_kyc_documents?).to eq(false)
      # end
    end

    context 'soletrader' do
      # it 'returns true when document exists' do
      #   user.legal_type = 'SOLETRADER'
      #
      #   user.mangopay_kyc_documents.create(kind: 'identity_proof', state: 'VALIDATED')
      #   user.mangopay_kyc_documents.create(kind: 'registration_proof', state: 'VALIDATED')
      #
      #   expect(user.valid_kyc_documents?).to eq(false)
      # end
    end
  end
end
