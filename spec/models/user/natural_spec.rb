require 'rails_helper'

RSpec.describe User::Natural, type: :model do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:kyc_regular_user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }
  let(:property) { create(:property_regular) }

  describe 'factories' do
    let(:one) { create(:user_natural) }
    let(:two) { create(:user_natural, :confirmed) }
    let(:three) { create(:user_natural, :confirmed, :kyc_light_complete) }
    let(:four) { create(:user_natural, :confirmed, :kyc_light_complete) }
    let(:five) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }

    it 'is valid' do
      expect(one.valid?).to eq(true)
      expect(two.valid?).to eq(true)
      expect(three.valid?).to eq(true)
      expect(four.valid?).to eq(true)
      expect(five.valid?).to eq(true)
    end
  end

  describe 'kyc_light_user validation' do
    subject { User::Natural.new(aasm_state: 'kyc_light_is_complete') }

    it { is_expected.to validate_presence_of(:country_of_residence) }
    it { is_expected.to validate_presence_of(:date_of_birth) }
    it { is_expected.to validate_presence_of(:nationality) }
    it { is_expected.to validate_presence_of(:address) }
    it { is_expected.to validate_presence_of(:title) }
  end

  describe 'kyc_regular_user validation' do
    subject { User::Natural.new(aasm_state: 'kyc_regular_is_pending') }

    it { is_expected.to validate_presence_of(:income_range) }
    it { is_expected.to validate_presence_of(:occupation) }
    it { is_expected.to validate_presence_of(:identity_proof) }
  end

  describe '.identity_proof' do
    let(:size) { 10.megabytes }

    before(:each) do
      allow(user).to receive(:identity_proof_required?).and_return(true)
    end

    it 'should be valid when less than 10 megabytes' do
      user.identity_proof = random_string(size - 1)

      expect(user.valid?).to eq(true)
    end

    it 'should be valid when exactly 10 megabytes' do
      user.identity_proof = random_string(size)

      expect(user.valid?).to eq(true)
    end

    it 'should be invalid when more than 10 megabytes' do
      user.identity_proof = random_string(size + 1)

      expect(user.valid?).to eq(false)
      expect(user.errors[:identity_proof].first).to eq('the maximum filesize is 10mb')
    end
  end

  describe '.valid_kyc_documents?' do
    # it 'returns true when document exists' do
    #   user.mangopay_kyc_documents.create(kind: 'identity_proof', state: 'VALIDATED')
    #
    #   expect(user.valid_kyc_documents?).to eq(false)
    # end

    it 'returns false when document doesnt exist' do
      user.kyc_documents.destroy_all

      expect(user.valid_kyc_documents?).to eq(false)
    end
  end
end

def random_string(length)
  SecureRandom.hex(length)[0,length]
end
