require 'rails_helper'

RSpec.describe User::QueuedActionInvestment, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to validate_presence_of(:target) }

  let(:user_queued_action_investment) { create(:user_queued_action_investment) }
  let(:total_amount) { user_queued_action_investment.potential_investment.total }

  describe '#process!' do
    context 'success' do
      it 'sets the correct status' do
        user_queued_action_investment.process!

        expect(user_queued_action_investment.aasm_state).to eq('completed')
      end
    end
  end

  describe '#potential_investment' do
    it 'returns the target' do
      expect(user_queued_action_investment.potential_investment).to eq(user_queued_action_investment.target)
    end
  end
end
