require 'rails_helper'

RSpec.describe User::QueuedActionPayout, type: :model do
  it { is_expected.to validate_presence_of(:amount) }
  it { is_expected.to validate_presence_of(:bank_account_id) }

  let(:user_queued_action_payout) { create(:user_queued_action_payout) }

  describe '#process!' do
    it 'sets the correct status' do
      user_queued_action_payout.process!

      expect(user_queued_action_payout.aasm_state).to eq('completed')
    end
  end
end
