require 'rails_helper'

RSpec.describe User::QueuedAction, type: :model do
  it { is_expected.to validate_presence_of(:user) }

  let(:user_queued_action) { create(:user_queued_action) }

  describe 'default state' do
    it 'is pending' do
      expect(user_queued_action.aasm_state).to eq('pending')
    end
  end

  describe '#process!' do
    it 'raises exception' do
      expect { user_queued_action.process! }.to raise_error(RuntimeError, 'Must be implemented by subclass')
    end
  end

  describe '#fail_with_message' do
    let(:message) { 'failure message' }

    it 'sets the status and message' do
      user_queued_action.send(:fail_with_message, message)

      expect(user_queued_action.aasm_state).to eq('failed')
      expect(user_queued_action.message).to eq(message)
    end
  end
end
