require 'rails_helper'

RSpec.describe User::State, type: :model do
  it { is_expected.to belong_to(:user) }

  it { is_expected.to validate_presence_of(:user) }
  it { is_expected.to validate_presence_of(:after) }

  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_required) }

  describe 'state logging' do
    it 'should log each state change' do
      expect(user.user_states.size).to eq(2)
      expect(User::State.find_by(user: user, before: 'registered', after: 'kyc_light_is_complete')).not_to eq(nil)
      expect(User::State.find_by(user: user, before: 'kyc_light_is_complete', after: 'kyc_regular_is_required')).not_to eq(nil)
    end
  end
end
