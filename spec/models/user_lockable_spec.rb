require 'rails_helper'

RSpec.describe User, type: :model do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }

  describe '#login_success!' do
    it 'creates user.login_attempt record' do
      expect { user.login_success! }.to change { user.login_attempts.where(state: 'success').count }.by(1)
    end
  end

  describe '#login_failure!' do
    it 'creates user.login_attempt record' do
      expect { user.login_failure! }.to change { user.login_attempts.where(state: 'fail').count }.by(1)
    end

    it 'locks my account if appropriate' do
      user.failed_attempts = described_class::MAXIMUM_ATTEMPTS

      expect(user.locked?).to eq(false)

      user.login_failure!

      expect(user.locked?).to eq(true)
    end
  end

  describe '#lock!' do
    it 'sets locked_at' do
      expect(user.locked_at).to eq(nil)

      user.lock!

      expect(user.locked_at).not_to eq(nil)
    end
  end

  describe '#unlock!' do
    before do
      user.lock!
      user.send(:increment_failed_attempts!)
    end

    it 'clears locked at' do
      expect(user.locked_at).not_to eq(nil)

      user.unlock!

      expect(user.locked_at).to eq(nil)
    end

    it 'clears failed_attempts' do
      expect(user.failed_attempts).not_to eq(0)

      user.unlock!

      expect(user.failed_attempts).to eq(0)
    end
  end

  describe '#locked?' do
    it 'returns true when appropriate' do
      expect(user.locked?).to eq(false)
    end

    it 'returns false when appropriate' do
      user.lock!
      expect(user.locked?).to eq(true)
    end
  end

  describe '#increment_failed_attempts!' do
    it 'adds one to login attempts and saves the model' do
      expect { user.send(:increment_failed_attempts!) }.to change(user, :failed_attempts).by(1)
    end
  end

  describe '#attempts_exceeded?' do
    it 'returns true if equal' do
      user.failed_attempts = described_class::MAXIMUM_ATTEMPTS
      expect(user.send(:attempts_exceeded?)).to eq(true)
    end

    it 'returns true if greater' do
      user.failed_attempts = described_class::MAXIMUM_ATTEMPTS + 1
      expect(user.send(:attempts_exceeded?)).to eq(true)
    end

    it 'returns false if under' do
      user.failed_attempts = described_class::MAXIMUM_ATTEMPTS - 1
      expect(user.send(:attempts_exceeded?)).to eq(false)
    end
  end

  describe '#lock_expired?' do
    it 'returns true when expired' do
      user.lock!
      user.locked_at = described_class::UNLOCK_IN.ago

      expect(user.send(:lock_expired?)).to eq(true)
    end

    it 'returns false when not expired' do
      user.lock!

      expect(user.locked_at).to be > described_class::UNLOCK_IN.ago
      expect(user.send(:lock_expired?)).to eq(false)
    end
  end

  describe '#unlock_time' do
    it 'is calculated correctly' do
      user.lock!

      expected_time = user.locked_at + described_class::UNLOCK_IN

      expect(user.send(:unlock_time)).to eq(expected_time)
    end
  end

  describe '#send_locked_notification' do
    it 'notify user and admin' do
      user.lock!

      expect { user.send(:send_locked_notification) }.to have_enqueued_job.on_queue('mailers').exactly(:twice)
    end
  end
end
