require 'rails_helper'
require 'timecop'

RSpec.describe User, type: :model do
  before { stub_nuapay_create_account_service }
  it { is_expected.to belong_to(:certification_level) }

  it { is_expected.to have_many(:addresses) }
  it { is_expected.to have_many(:certification_attempts) }
  it { is_expected.to have_many(:dividends) }
  it { is_expected.to have_many(:login_attempts) }
  it { is_expected.to have_many(:mandates) }
  it { is_expected.to have_many(:news_items) }
  it { is_expected.to have_many(:payment_logs) }
  it { is_expected.to have_many(:potential_investments) }
  it { is_expected.to have_many(:queued_actions) }
  it { is_expected.to have_many(:share_orders) }
  it { is_expected.to have_many(:buy_orders) }
  it { is_expected.to have_many(:sell_orders) }
  it { is_expected.to have_many(:share_logs) }

  it { is_expected.to have_many(:kyc_documents) }
  it { is_expected.to have_many(:mangopay_kyc_documents) }
  it { is_expected.to have_many(:onfido_kyc_documents) }

  it { is_expected.to have_secure_password }
  it { is_expected.to validate_presence_of(:email) }
  it { is_expected.to validate_uniqueness_of(:email).case_insensitive }

  it { is_expected.to validate_length_of(:last_name).is_at_least(2).allow_nil }

  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:kyc_regular_user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }
  let(:property) { create(:property_regular) }

  describe 'authenticate_from_token' do
    before do
      Timecop.freeze
      user.ensure_authentication_token!
    end

    it 'authenticates correctly when session is active' do
      expect(User.authenticate_from_token(user.id, user.authentication_token)).to eq(user)
    end

    it 'fails correctly on invalid details' do
      result = User.authenticate_from_token(user.id, 'ABCEDG123456789')

      expect(result).to eq(nil)
    end

    it 'times out correctly' do
      expect(User.authenticate_from_token(user.id, user.authentication_token)).to eq(user)

      Timecop.travel(Time.now + 1.hour)

      expect(User.authenticate_from_token(user.id, user.authentication_token)).to eq(nil)
    end
  end

  describe 'email_validation' do
    let(:valid_emails) do
      ['<EMAIL>',
       '<EMAIL>']
    end

    it 'valid emails' do
      valid_emails.each do |email|
        user.email = email
        user.valid?

        expect(user.errors[:email].size).to eq(0)
      end
    end

    let(:invalid_emails) do
      ['test@example',
       ' <EMAIL>',
       'example.com',
       '<EMAIL> ']
    end

    it 'invalid emails' do
      invalid_emails.each do |email|
        user.email = email
        user.valid?

        expect(user.errors[:email].size).to eq(1)
      end
    end
  end

  describe '#bank_accounts' do
    it 'should return a list of bank accounts for a user' do
      # expect(user.bank_accounts.size).to be > 0
    end
  end

  describe '#reject_if_invalid_country!' do
    it 'should allow allowed countries' do
      expect(user.valid?).to eq(true)

      country_codes = ISO3166::Country.codes - User::DISALLOWED_COUNTRIES

      country_codes.each do |country_code|
        user.country_of_residence = country_code
        user.save!

        expect(user.aasm_state).to eq('kyc_light_is_complete')
      end
    end

    it 'should not allow non allowed countries' do
      expect(user.valid?).to eq(true)

      country_codes = User::DISALLOWED_COUNTRIES

      country_codes.each do |country_code|
        user.country_of_residence = country_code
        user.save!

        expect(user.aasm_state).to eq('rejected_country')
      end
    end
  end

  describe '#reject_if_invalid_nationality!' do
    it 'should allow allowed nationalities' do
      expect(user.valid?).to eq(true)

      country_codes = ISO3166::Country.codes - User::DISALLOWED_NATIONALITIES

      country_codes.each do |country_code|
        user.nationality = country_code
        user.save!

        expect(user.aasm_state).to eq('kyc_light_is_complete')
      end
    end

    it 'should not allow non allowed nationalities' do
      expect(user.valid?).to eq(true)

      country_codes = User::DISALLOWED_NATIONALITIES

      country_codes.each do |country_code|
        user.nationality = country_code
        user.save!

        expect(user.aasm_state).to eq('rejected_nationality')
      end
    end
  end

  describe '#reject_if_under_age!' do
    let(:min_age) { User::MIN_AGE }

    it 'over 18s can register' do
      user.date_of_birth = (min_age + 1).years.ago
      user.save!

      expect(user.aasm_state).to eq('kyc_light_is_complete')
    end

    it 'under 18s cant register' do
      user.date_of_birth = (min_age - 1).years.ago
      user.save!

      expect(user.aasm_state).to eq('rejected_age')
    end
  end

  describe '#amount_debited' do
    let(:create_payment_logs) { 10.times { create(:payment_log, :debit, user: user).successful!('SUCCEEDED') } }
    let(:create_old_log) { create(:payment_log, :debit, user: user, created_at: 2.years.ago).successful!('SUCCEEDED') }
    let(:create_pending_log) { create(:payment_log, :debit, user: user) }
    let(:create_unsuccessful_log) { create(:payment_log, :debit, user: user).failure!('FAILED') }
    let(:expected_total) do
      PaymentLog.where(user: user)
                .where(failed_at: nil)
                .where(direction: 'debit')
                .where(created_at: (Time.now - 1.year)..Time.now)
                .sum(:amount)
    end

    before(:each) do
      create_payment_logs
      create_pending_log
    end

    it 'should correctly calculate total for this year' do
      expect(user.amount_debited).to eq(expected_total)
    end

    it 'shouldnt include items from last year' do
      create_old_log

      expect(user.amount_debited).to eq(expected_total)
    end

    it 'shouldnt include non successful items' do
      create_unsuccessful_log

      expect(user.amount_debited).to eq(expected_total)
    end
  end

  describe '.amount_credited' do
    let(:create_payment_logs) { 10.times { create(:payment_log, user: user).successful!('SUCCEEDED') } }
    let(:create_old_log) { create(:payment_log, user: user, created_at: 2.years.ago).successful!('SUCCEEDED') }
    let(:create_pending_log) { create(:payment_log, user: user) }
    let(:create_unsuccessful_log) { create(:payment_log, user: user).failure!('FAILED') }
    let(:expected_total) do
      PaymentLog.where(user: user)
                .where(failed_at: nil)
                .where(direction: 'credit')
                .where(created_at: (Time.now - 1.year)..Time.now)
                .sum(:amount)
    end

    before(:each) do
      create_payment_logs
      create_pending_log
    end

    it 'should correctly calculate total for this year' do
      expect(user.amount_credited).to eq(expected_total)
    end

    it 'shouldnt include items from last year' do
      create_old_log

      expect(user.amount_credited).to eq(expected_total)
    end

    it 'shouldnt include non successful items' do
      create_unsuccessful_log

      expect(user.amount_credited).to eq(expected_total)
    end
  end

  describe '.check_kyc_limitations!' do
    let(:max_credit) { User::MAX_KYC_CREDIT }
    let(:create_credit_payment_log) { create(:payment_log, user: user, amount: max_credit + 1).successful!('SUCCEEDED') }

    describe 'invalid state' do
      it 'nothing happens when the user is in the incorrect state' do
        # Call method for every state that should be skipped
        ['kyc_regular_is_required', 'kyc_regular_is_complete',
         'kyc_regular_is_pending', 'kyc_regular_has_failed'].each do |state|
          user.aasm_state = state
          user.check_kyc_limitations!

          expect(user.aasm_state).to eq(state)
        end
      end
    end

    describe 'valid state' do
      it 'should change the users state when over credit limit' do
        expect(user.aasm_state).to eq('kyc_light_is_complete')
        create_credit_payment_log

        expect(user.amount_credited).to be > max_credit
        expect(user.aasm_state).to eq('kyc_regular_is_required')
      end
    end
  end

  describe '.over_kyc_limitations?' do
    let(:max_credit) { User::MAX_KYC_CREDIT }
    let(:create_credit_payment_log) { create(:payment_log, user: user, amount: max_credit - 1).successful!('SUCCEEDED') }

    describe 'additional amounts' do
      it 'should accept an additional credit amount' do
        expect(user.aasm_state).to eq('kyc_light_is_complete')
        expect(user.over_kyc_limitations?(credit: max_credit + 1)).to eq(true)
      end
    end

    describe 'state' do
      it 'returns true if user is already kyc regular' do
        user.aasm_state = 'kyc_regular_is_complete'
        expect(user.over_kyc_limitations?).to eq(false)
      end

      it 'should return true when over credit limit' do
        expect(user.aasm_state).to eq('kyc_light_is_complete')
        create_credit_payment_log

        expect(user.over_kyc_limitations?(credit: 2)).to eq(true)
      end
    end
  end

  describe '#previous_state' do
    it 'returns their last state' do
      expected_state = user.user_states.last.before

      expect(user.previous_state).to eq(expected_state)
    end

    it 'skips the last state if its the same as their current state' do
      previous_state = user.user_states.last

      # Set their previous state change to the same as their current state to replicate an old bug.
      user.user_states.create!(before: user.aasm_state, after: user.aasm_state)

      expect(user.previous_state).to eq(previous_state.before)
    end
  end

  describe '#shares_available_for_property' do
    let(:order_quantity) { 2 }
    let(:result) { user.shares_available_for_property(property) }

    context 'regular user' do
      let(:quantity) { 5 }
      let!(:create_share_logs) { create(:share_log, user: user, property: property, quantity: quantity) }

      it 'removes any active sell orders' do
        expect(result).to eq(quantity)
      end

      it 'removes any pending transfer orders' do
        create(:share_transfer_order, source_user: user, property: property, quantity: order_quantity)
        expected_total = user.share_logs.where(property: property).sum(:quantity) - order_quantity

        expect(result).to eq(expected_total)
      end

      it 'removes any pending easy exit orders' do
        create(:share_easy_exit_order, user: user, property: property, quantity: order_quantity)
        expected_total = user.share_logs.where(property: property).sum(:quantity) - order_quantity

        expect(result).to eq(expected_total)
      end

      it 'removes any pending exit orders' do
        create(:share_exit_order, user: user, property: property, quantity: order_quantity)
        expected_total = user.share_logs.where(property: property).sum(:quantity) - order_quantity

        expect(result).to eq(expected_total)
      end
    end

    context 'root user' do
      let(:user) { User.admin_uown }

      it 'excludes active sell orders' do
        expect(result).to eq(property.share_count)
      end
    end
  end

  describe 'kyc_regular_submitted!' do
    it 'should be valid' do
      expect(kyc_regular_user.valid?).to eq(true)
    end
  end

  describe 'kyc_regular_failed!' do
    it 'change the state' do
      kyc_regular_user.kyc_regular_failed!

      expect(kyc_regular_user.aasm_state).to eq('kyc_regular_has_failed')
    end
  end

  describe 'send_admin_notification' do
    it 'sends an admin notification' do
      expect { user }.to have_enqueued_job.on_queue('mailers')
    end
  end

  describe '#format_attributes' do
    let(:user) do
     build(
       :user_natural,
       first_name: 'first name',
       middle_name: "middle'name",
       last_name: 'last-name',
       email: '<EMAIL>'
      )
    end

    it 'titleizes each name section' do
      user.save

      expect(user.first_name).to eq('First Name')
      expect(user.middle_name).to eq("Middle'Name")
      expect(user.last_name).to eq('Last-Name')
      expect(user.email).to eq('<EMAIL>')
    end
  end

  describe '#process_referer' do
    it 'extracts the domain' do
      user.update(referer: 'http://google.com/an/example')

      expect(user.reload.referer_host).to eq('google.com')
    end

    it 'falls back on non urls' do
      user.update(referer: 'example')

      expect(user.reload.referer_host).to eq('example')
    end
  end

  describe '#refresh_kyc_status!' do
    describe '.valid_kyc_documents?' do
      let(:image_data) { 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg==' }

      before do
        kyc_regular_user.mangopay_kyc_documents.create!(kind: 'identity_proof', state: 'VALIDATION_ASKED', base_64_image: image_data)
        ActionMailer::Base.deliveries.clear
      end

      # it 'when valid' do
        # allow_any_instance_of(Mango::KYC).to receive(:status).and_return('VALIDATED')
        #
        # expect(kyc_regular_user.refresh_kyc_status!).to eq(true)
        # expect(kyc_regular_user.aasm_state).to eq('kyc_regular_is_complete')
      # end

      # it 'when rejected' do
        # allow_any_instance_of(Mango::KYC).to receive(:status).and_return('REFUSED')
        # allow_any_instance_of(User::MangopayKycDocument).to receive(:failure_reason).and_return('document_unreadable')
        #
        # expect(kyc_regular_user.refresh_kyc_status!).to eq(false)
        # expect(kyc_regular_user.aasm_state).to eq('kyc_regular_has_failed')
      # end

      # it 'when pending' do
        # allow_any_instance_of(Mango::KYC).to receive(:status).and_return('VALIDATION_ASKED')
        #
        # expect(kyc_regular_user.refresh_kyc_status!).to eq(false)
        # expect(kyc_regular_user.aasm_state).to eq('kyc_regular_is_pending')
      # end
    end
  end
end
