require 'rails_helper'

shared_examples 'it acts like a property payout' do
  ## initialization

  describe '#build_default_fees' do
    it 'builds a Property::PayoutFee for each Property::Payout.default_fees' do
      payout.build_default_fees

      expect(payout.fees.size).to eq(payout.class.default_fees.size)
      expect(payout.fees.collect(&:description)).to eq(payout.class.default_fees)
    end
  end

  ## after create

  describe '#create_dividends' do
    it 'builds a dividend for each item in grouped_dividends' do
      expected_amount = payout.grouped_dividends.size

      expect(payout.dividends.size).to be > 0
      expect(payout.dividends.size).to eq(expected_amount)
    end
  end

  ## validation

  describe '#check_subtotal_positive' do
    it 'throws a validation error if subtotal is negative' do
      payout.fees.build(amount: payout.amount + 1)

      expect(payout.subtotal).to be < 0
      expect(payout.valid?).to be(false)
      expect(payout.errors[:amount_in_pounds].first).to eq('subtotal cannot be negative')
    end
  end

  ## calculations

  describe '#allocation_amount' do
    it 'returns the sum of amount to pay plus fees' do
      expected_amount = payout.total_fees +
                        payout.dividends.sum(&:amount_to_pay)
                        payout.exit_orders.sum(&:total_amount)

      expect(payout.allocation_amount).to eq(expected_amount)
    end
  end

  describe '#grouped_shares_on' do
    let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
    let(:quantity) { 5 }

    let(:build_share_logs) { create_list(:share_log, quantity, property: payout.property, quantity: 1) }

    let(:build_share_logs_for_one_user) do
      create(:share_log, property: payout.property, quantity: 1, user: user)
    end

    let(:build_old_share_logs_for_one_user) do
      create_list(:share_log, quantity, property: payout.property, quantity: 1, created_at: 2.weeks.ago, user: user)
    end

    it 'is grouped shares by user' do
      build_share_logs
      build_share_logs_for_one_user

      expect(payout.send(:grouped_shares_on).to_a.size).to be > 0

      payout.send(:grouped_shares_on).each do |share_group|
        expected_total = Share::Log.where(property: share_group.property,
                                          user: share_group.user).sum(:quantity)

        expect(share_group.total).to eq(expected_total)
      end
    end

    it 'accepts a date range' do
      build_share_logs
      build_share_logs_for_one_user
      build_old_share_logs_for_one_user

      date = Date.today - 1.week

      expect(payout.send(:grouped_shares_on, date).to_a.size).to be > 0
      expect(payout.send(:grouped_shares_on, date)).not_to eq(payout.send(:grouped_shares_on))

      payout.send(:grouped_shares_on, date).each do |share_group|
        expected_total = Share::Log.where(property: share_group.property, user: share_group.user)
                                   .where('created_at < ?', date + 1.day)
                                   .sum(:quantity)

        expect(share_group.total).to eq(expected_total)
      end
    end
  end

  ## helpers

  describe '#total_fees' do
    it 'is the sum of all the fee items' do
      payout.build_default_fees
      payout.fees.each do |fee|
        fee.amount = 1
      end

      expected_total = payout.fees.collect(&:amount).sum

      expect(payout.total_fees).to eq(expected_total)
    end
  end

  describe '#subtotal' do
    it 'is the total minus any line items' do
      expected_total = payout.amount - payout.total_fees

      expect(payout.subtotal).to eq(expected_total)
    end
  end

  describe '#total_per_share' do
    it 'is a decimal' do
      expect(payout.total_per_share.class).to eq(BigDecimal)
    end
  end

  describe '#total_shares' do
    it 'is the sum of all share_orders for selected property' do
      expected_total = Share::Log.where(property_id: payout.property_id).sum(:quantity)

      expect(expected_total).not_to eq(0)
      expect(payout.total_shares).to eq(expected_total)
    end
  end

  ## state changes

  describe 'funds_available?' do
    let(:amount) { payout.allocation_amount }

    context 'with enough funds' do
      before { stub_nuapay_account_balance_service }

      it 'returns true' do
        expect(payout.funds_available?).to eq(true)
      end
    end

    context 'without enough funds' do
      before { stub_nuapay_account_balance_service(balance: 0) }

      it 'returns false' do
        expect(payout.funds_available?).to eq(false)
      end
    end
  end

  describe '#allocate_funds!' do
    before { stub_nuapay_account_balance_service }
    it 'changes state on success' do
      payout.allocate_funds!

      expect(payout.aasm_state).to eq('funds_allocated')
    end
  end

  describe '#transfer_fees!' do
    before { stub_nuapay_account_balance_service }
    it 'changes state on success' do
      payout.allocate_funds!
      payout.transfer_fees!

      expect(payout.aasm_state).to eq('fees_transferred')
    end
  end

  describe '#distribute_dividends!' do
    before do
      stub_nuapay_account_balance_service
      stub_nuapay_process_dividends_service
    end
    it 'changes state on success' do
      payout.allocate_funds!
      payout.transfer_fees!
      payout.distribute_dividends!

      expect(payout.aasm_state).to eq('dividends_distributed')
    end
  end
end
