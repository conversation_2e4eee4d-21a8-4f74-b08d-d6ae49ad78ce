require 'rails_helper'

shared_examples 'it acts like a sell order' do
  describe '.check_sell_order' do
    it 'should return if property is blank' do
      share_order.user = user
      expect(share_order.send(:check_sell_order)).to eq(nil)
    end

    it 'should return if user is blank' do
      share_order.property = valid_property
      expect(share_order.send(:check_sell_order)).to eq(nil)
    end

    it 'should add validation error if property is unfunded / not easy exit' do
      share_order.user = user
      share_order.property = invalid_property
      add_shares

      expect(share_order.valid?).to eq(false)
      expect(share_order.errors[:base]).to eq(["You can't sell shares in this property"])
    end

    it 'should add validation error if user doesnt have enough shares' do
      share_order.user = user
      share_order.property = valid_property

      expect(share_order.valid?).to eq(false)
      expect(share_order.errors[:base]).to eq(["You don't have enough shares to sell that many"])
    end
  end

  describe '.cancel' do
    it 'should always fail' do
      expect { share_order.cancel! }.to raise_error(AASM::InvalidTransition)
    end
  end
end
