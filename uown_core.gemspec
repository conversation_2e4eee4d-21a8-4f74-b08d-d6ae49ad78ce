$LOAD_PATH.push File.expand_path('lib', __dir__)

# Maintain your gem's version:
require 'uown_core/version'

# Describe your gem and declare its dependencies:
Gem::Specification.new do |s|
  s.name        = 'uown_core'
  s.version     = UownCore::VERSION
  s.authors     = ['<PERSON>ip<PERSON>']
  s.email       = ['<EMAIL>']
  s.homepage    = 'http://www.uown.co'
  s.summary     = 'uOwn Core'
  s.description = 'Core models and migrations for UOWN property investment website'

  s.files = Dir['{app,config,db,lib}/**/*', 'MIT-LICENSE', 'Rakefile', 'README.rdoc']
  s.test_files = Dir['test/**/*']

  s.required_ruby_version = Gem::Requirement.new('>= 3.3.6')

  s.add_dependency 'aasm', '5.2.0'
  s.add_dependency 'active_storage_validations', '1.0.1'
  s.add_dependency 'after_commit_everywhere', '~> 1.0'
  s.add_dependency 'aws-sdk-s3', '1.105.1'
  s.add_dependency 'bcrypt', '3.1.12'
  s.add_dependency 'countries', '4.0.1'
  s.add_dependency 'image_processing'
  s.add_dependency 'password_strength', '1.1.4'
  s.add_dependency 'rails', '~> 7.0.1'
  s.add_dependency 'sidekiq', '7.2.4'
  s.add_dependency  'sprockets-rails'

  # Two Factor Authentication
  s.add_dependency 'authy', '3.0.0'

  # Payment Gateway
  s.add_dependency 'mangopay', '3.8.0'

  # Address parsing
  s.add_dependency 'uk_postcode', '2.1.6'

  # HAML for email templates
  s.add_dependency 'haml-rails', '2.0.1'

  # Flag notification
  s.add_dependency 'noticed', '2.2.2'
  s.add_dependency 'active_delivery', '~> 1.2'

  # Rest client
  s.add_dependency 'rest-client'

  # # UUIDv7
  s.add_dependency 'uuid7'

  # IBAN
  s.add_dependency 'ibandit'

  # Test Framework
  s.add_development_dependency 'factory_bot', '~> 6.2.0'
  s.add_development_dependency 'rspec-collection_matchers', '~> 1.2.0'
  s.add_development_dependency 'rspec-rails', '~> 5.0.2'
  s.add_development_dependency 'shoulda-matchers', '~> 5.0.0'
  s.add_development_dependency 'simplecov', '~> 0.21.2'
  s.add_development_dependency 'sqlite3', '~> 1.4'
  s.add_development_dependency 'timecop', '~> 0.9.4'
  s.add_development_dependency 'webmock', '~> 3.14.0'
end
